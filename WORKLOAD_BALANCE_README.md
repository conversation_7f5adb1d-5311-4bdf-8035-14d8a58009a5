# Workload Balance Optimization for MRC Scheduling

This document describes the workload balance optimization features added to the MRC scheduling system.

## Overview

The system now supports multi-objective optimization combining:
1. **Makespan Minimization**: Minimize the total time to complete all tasks
2. **Workload Balance**: Minimize the variation in task assignments across robots

## Key Features

### 1. Workload Balance Metric
- **Definition**: Standard deviation of task counts per robot
- **Goal**: Minimize variance to achieve balanced workloads
- **Score**: 0.0 = perfectly balanced, higher values = more imbalanced

### 2. Multi-Objective Optimization
- **Weighted Sum Approach**: Combines makespan and workload balance objectives
- **Configurable Weights**: α (makespan) + β (workload balance) = 1.0
- **Pareto Front Analysis**: Identifies non-dominated solutions

### 3. New Solver Options
- `workload_balance`: Pure workload balance optimization using Gurobi
- `multi_objective`: Multi-objective optimization with configurable weights
- `balance_heuristic`: Fast heuristic for workload balance

## Files Added/Modified

### New Files
1. **`multi_objective_utils.py`**: Core utilities for multi-objective optimization
   - Workload balance calculation
   - Weighted objective combination
   - Pareto front analysis
   - Multi-objective metrics tracking

2. **`workload_balance_solvers.py`**: Specialized solvers for workload balance
   - Gurobi-based exact optimization
   - Multi-objective Gurobi solver
   - Heuristic round-robin solver

3. **`multi_objective_evaluation.py`**: Comprehensive evaluation script
   - Evaluates all solvers on test instances
   - Generates Pareto front plots
   - Produces detailed statistics

4. **`test_workload_balance.py`**: Test script for verification
   - Unit tests for workload balance calculation
   - Integration tests with real instances
   - Validation of multi-objective metrics

5. **`WORKLOAD_BALANCE_README.md`**: This documentation file

### Modified Files
1. **`utils.py`**: Extended SchedulingEnv class
   - Added multi-objective parameters
   - New workload balance calculation method
   - Multi-objective reward calculation
   - Configuration methods for objective weights

2. **`makespan_for_each.py`**: Extended solver interface
   - Added new solver options
   - Multi-objective parameter support
   - Integration with workload balance solvers

## Usage Examples

### 1. Pure Workload Balance Optimization
```bash
python3 makespan_for_each.py \
    --solver workload_balance \
    --path-test-data ./problem_instances_test/constraints \
    --num-tasks 5 \
    --num-robots 2 \
    --max-instances 100 \
    --output-folder ./results
```

### 2. Multi-Objective Optimization
```bash
python3 makespan_for_each.py \
    --solver multi_objective \
    --path-test-data ./problem_instances_test/constraints \
    --num-tasks 5 \
    --num-robots 2 \
    --max-instances 100 \
    --output-folder ./results \
    --alpha 0.7 \
    --beta 0.3
```

### 3. Comprehensive Multi-Objective Evaluation
```bash
python3 multi_objective_evaluation.py \
    --path-test-data ./problem_instances_test/constraints \
    --num-tasks 5 \
    --num-robots 2 \
    --max-instances 100 \
    --output-folder ./multi_obj_results
```

### 4. Test Implementation
```bash
python3 test_workload_balance.py
```

## Configuration Parameters

### Multi-Objective Weights
- **`--alpha`**: Weight for makespan objective (0-1, default: 0.5)
- **`--beta`**: Weight for workload balance objective (0-1, default: 0.5)
- **Note**: α + β should equal 1.0 for proper normalization

### Weight Combinations
- **α=0.8, β=0.2**: Favor makespan (80% makespan, 20% balance)
- **α=0.5, β=0.5**: Equal importance (50% makespan, 50% balance)
- **α=0.2, β=0.8**: Favor workload balance (20% makespan, 80% balance)

## Output Files

### 1. CSV Results
- **`makespans_[solver].csv`**: Individual solver results
- **`multi_objective_results.csv`**: Comprehensive multi-objective results
- **`summary_statistics.csv`**: Aggregated statistics per solver

### 2. Visualizations
- **`pareto_front.png`**: Pareto front plot showing trade-offs
- **Additional plots**: Generated by evaluation scripts

### 3. Statistics
- Feasibility rates per solver
- Average makespan and workload balance
- Runtime comparisons
- Pareto front analysis

## Implementation Details

### Workload Balance Calculation
```python
def calculate_workload_balance(assignments, num_robots):
    task_counts = np.zeros(num_robots)
    for robot_id, tasks in assignments.items():
        task_counts[robot_id] = len(tasks)
    return np.std(task_counts)  # Standard deviation
```

### Multi-Objective Formulation
```
Objective = α × (makespan / makespan_weight) + β × (balance / balance_weight)
```

### Gurobi MILP Formulation
The multi-objective Gurobi solver adds:
- Task count variables per robot
- Average task count constraint
- Maximum deviation minimization
- Weighted objective combination

## Performance Considerations

### Computational Complexity
- **Workload Balance Only**: Similar to makespan-only optimization
- **Multi-Objective**: Slightly increased due to additional constraints
- **Heuristic**: Fast O(n) assignment with feasibility checking

### Scalability
- Tested on instances with 5-20 tasks and 2-4 robots
- Gurobi solvers scale well to larger instances
- Heuristic solver provides fast approximations

## Future Extensions

### Possible Enhancements
1. **Additional Objectives**: Energy consumption, travel distance
2. **Advanced Algorithms**: NSGA-II, MOEA/D for large instances
3. **Dynamic Weights**: Adaptive weight adjustment during optimization
4. **Robustness**: Uncertainty handling in task durations

### Integration Opportunities
1. **SSAN Training**: Multi-objective reward in neural network training
2. **Online Scheduling**: Real-time workload balancing
3. **Hierarchical Optimization**: Two-stage makespan then balance optimization

## Validation Results

The implementation has been validated with:
- Unit tests for workload balance calculation
- Integration tests with existing solvers
- Comparison against makespan-only baselines
- Pareto front verification for multi-objective solutions

## Contact and Support

For questions or issues related to workload balance optimization:
1. Check the test script: `python3 test_workload_balance.py`
2. Review the evaluation output for debugging
3. Examine the Pareto front plots for solution quality assessment
