#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
run_complete_evaluation.py

Complete pipeline to test models individually and generate evaluation plots.
"""

import os
import subprocess
import argparse
import sys


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"STEP: {description}")
    print(f"{'='*60}")
    print(f"Command: {command}")
    print()
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=False, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed with return code {e.returncode}")
        return False
    except Exception as e:
        print(f"✗ {description} failed with error: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Complete evaluation pipeline")
    parser.add_argument("--model-path", 
                       default="./cp_multi_obj/checkpoint_00500.tar",
                       help="Path to trained model checkpoint")
    parser.add_argument("--test-data", 
                       default="./problem_instances_test/constraints",
                       help="Path to test data directory")
    parser.add_argument("--num-tasks", default=5, type=int, help="Number of tasks")
    parser.add_argument("--num-robots", default=2, type=int, help="Number of robots")
    parser.add_argument("--max-instances", default=20, type=int, help="Maximum instances to test")
    parser.add_argument("--output-dir", default="./evaluation_results", help="Output directory")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    parser.add_argument("--skip-baselines", action="store_true", help="Skip baseline testing")
    
    args = parser.parse_args()
    
    print("COMPLETE MULTI-OBJECTIVE EVALUATION PIPELINE")
    print("=" * 60)
    print(f"Model: {args.model_path}")
    print(f"Test data: {args.test_data}")
    print(f"Problem size: {args.num_tasks} tasks, {args.num_robots} robots")
    print(f"Max instances: {args.max_instances}")
    print(f"Output directory: {args.output_dir}")
    print(f"Device: {args.device}")
    
    # Check if model file exists
    if not os.path.isfile(args.model_path):
        print(f"\n✗ Model file not found: {args.model_path}")
        print("Please train a model first or provide correct path")
        return False
    
    # Check if test data exists
    if not os.path.isdir(args.test_data):
        print(f"\n✗ Test data directory not found: {args.test_data}")
        return False
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Step 1: Test individual models
    test_command = f"""python3 test_individual_models.py \
        --model-path "{args.model_path}" \
        --test-data "{args.test_data}" \
        --num-tasks {args.num_tasks} \
        --num-robots {args.num_robots} \
        --max-instances {args.max_instances} \
        --output-dir "{args.output_dir}" \
        --device {args.device}"""
    
    if not args.skip_baselines:
        test_command += " --test-baselines"
    
    success = run_command(test_command, "Testing individual models")
    if not success:
        print("\n✗ Model testing failed. Cannot proceed with evaluation.")
        return False
    
    # Step 2: Evaluate and plot results
    eval_command = f"""python3 evaluate_and_plot_results.py \
        --results-dir "{args.output_dir}" \
        --output-dir "{args.output_dir}" """
    
    success = run_command(eval_command, "Evaluating results and generating plots")
    if not success:
        print("\n✗ Evaluation failed.")
        return False
    
    # Step 3: Display summary
    print(f"\n{'='*60}")
    print("PIPELINE COMPLETED SUCCESSFULLY")
    print(f"{'='*60}")
    
    # List generated files
    output_files = []
    for file_pattern in ["*.csv", "*.png", "*.txt"]:
        import glob
        files = glob.glob(os.path.join(args.output_dir, file_pattern))
        output_files.extend(files)
    
    if output_files:
        print(f"\nGenerated files in {args.output_dir}:")
        for file_path in sorted(output_files):
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"  - {file_name} ({file_size:,} bytes)")
    
    # Show key results
    summary_file = os.path.join(args.output_dir, "summary_statistics.csv")
    if os.path.isfile(summary_file):
        print(f"\n{'='*60}")
        print("KEY RESULTS PREVIEW")
        print(f"{'='*60}")
        
        try:
            import pandas as pd
            summary_df = pd.read_csv(summary_file)
            
            print("\nModel Performance Summary:")
            print(summary_df[['model_name', 'feasibility_rate', 'avg_makespan', 'avg_workload_balance']].to_string(index=False, float_format='%.3f'))
            
            # Find best performers
            feasible_models = summary_df[summary_df['feasibility_rate'] > 0]
            if not feasible_models.empty:
                best_makespan = feasible_models.loc[feasible_models['avg_makespan'].idxmin()]
                best_balance = feasible_models.loc[feasible_models['avg_workload_balance'].idxmin()]
                
                print(f"\nBest Performers:")
                print(f"  Best Makespan: {best_makespan['model_name']} ({best_makespan['avg_makespan']:.2f})")
                print(f"  Best Balance: {best_balance['model_name']} ({best_balance['avg_workload_balance']:.3f})")
        
        except Exception as e:
            print(f"Could not display summary: {e}")
    
    print(f"\n{'='*60}")
    print("NEXT STEPS")
    print(f"{'='*60}")
    print(f"1. Review detailed results in: {args.output_dir}")
    print(f"2. Check plots: performance_comparison.png, pareto_front.png")
    print(f"3. Read full report: evaluation_report.txt")
    print(f"4. Analyze individual model CSV files for detailed insights")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
