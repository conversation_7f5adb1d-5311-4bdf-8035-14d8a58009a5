#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_training_datatypes.py

Test script to verify data types are correct for training.
"""

import os
import numpy as np
import torch
from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper


def test_data_types():
    """Test that all data types are consistent for PyTorch training."""
    print("=== Testing Data Types for Training ===")
    
    # Find a test instance
    test_instance = None
    for inst_no in range(1, 6):
        fname = os.path.join("./problem_instances/constraints", f"{inst_no:05d}")
        if os.path.isfile(f"{fname}_dur.txt"):
            test_instance = fname
            break
    
    if not test_instance:
        print("No test instance found")
        return False
    
    print(f"Using test instance: {test_instance}")
    
    try:
        # Load environment
        env = SchedulingEnv(test_instance)
        env.set_multi_objective_params(alpha=0.5, beta=0.5)
        
        print(f"Environment: {env.num_tasks} tasks, {env.num_robots} robots")
        print(f"Duration matrix shape: {env.dur.shape}, dtype: {env.dur.dtype}")
        
        # Test basic operations
        unsch_tasks = env.get_unscheduled_tasks()
        print(f"Unscheduled tasks: {len(unsch_tasks)}")
        
        if len(unsch_tasks) == 0:
            print("No unscheduled tasks available")
            return False
        
        # Test graph building with correct data types
        print("\nTesting graph building...")
        
        g = build_hetgraph(
            env.halfDG,
            env.num_tasks,
            env.num_robots,
            env.dur.astype(np.float32),  # Ensure float32
            6,  # map_width
            np.array(env.loc, dtype=np.int64),
            1,  # loc_dist_threshold
            env.partials,
            np.array(unsch_tasks, dtype=np.int32),
            0,  # robot
            np.array(unsch_tasks, dtype=np.int32)
        )
        
        print(f"✓ Graph built successfully: {g.number_of_nodes()} nodes")
        
        # Test feature building
        print("\nTesting feature building...")
        
        feat_dict = hetgraph_node_helper(
            env.halfDG.number_of_nodes(),
            env.partialw,
            env.partials,
            env.loc,
            env.dur,
            6,  # map_width
            env.num_robots,
            len(unsch_tasks)
        )
        
        print(f"✓ Features built: {list(feat_dict.keys())}")
        
        # Test tensor conversion
        print("\nTesting tensor conversion...")
        device = torch.device('cpu')
        
        feat_tensors = {}
        for k, v in feat_dict.items():
            tensor = torch.tensor(v, device=device, dtype=torch.float32)
            feat_tensors[k] = tensor
            print(f"  {k}: shape={tensor.shape}, dtype={tensor.dtype}")
        
        print("✓ All tensors converted to float32")
        
        # Test reward calculation
        print("\nTesting reward calculation...")
        
        # Insert a task and get reward
        task = unsch_tasks[0]
        robot = 0
        
        success, reward, done_flag = env.insert_robot(task, robot)
        
        if success:
            print(f"✓ Task insertion successful")
            print(f"  Reward: {reward} (type: {type(reward)})")
            
            # Test reward tensor conversion
            reward_tensor = torch.tensor([[float(reward)]], device=device, dtype=torch.float32)
            print(f"  Reward tensor: {reward_tensor.dtype}")
            
        else:
            print("✗ Task insertion failed")
            return False
        
        print("\n✓ All data type tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_simple_training_step():
    """Test a simple training step to verify everything works."""
    print("\n=== Testing Simple Training Step ===")
    
    try:
        from hetnet import ScheduleNet4Layer
        
        # Network setup
        in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
        hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
        out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
        cetypes = [
            ('task', 'temporal', 'task'),
            ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
            ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
            ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
            ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
            ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
            ('state', 'sto', 'value'), ('value', 'vto', 'value'),
            ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
        ]
        
        device = torch.device('cpu')
        policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
        optimizer = torch.optim.Adam(policy_net.parameters(), lr=1e-4)
        
        print("✓ Network and optimizer created")
        
        # Find test instance
        test_instance = None
        for inst_no in range(1, 6):
            fname = os.path.join("./problem_instances/constraints", f"{inst_no:05d}")
            if os.path.isfile(f"{fname}_dur.txt"):
                test_instance = fname
                break
        
        if not test_instance:
            print("No test instance found")
            return False
        
        # Load environment and prepare data
        env = SchedulingEnv(test_instance)
        env.set_multi_objective_params(alpha=0.5, beta=0.5)
        
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            print("No unscheduled tasks")
            return False
        
        # Build graph and features
        g = build_hetgraph(
            env.halfDG,
            env.num_tasks,
            env.num_robots,
            env.dur.astype(np.float32),
            6, np.array(env.loc, dtype=np.int64), 1,
            env.partials, np.array(unsch_tasks, dtype=np.int32),
            0, np.array(unsch_tasks, dtype=np.int32)
        ).to(device)
        
        feat_dict = hetgraph_node_helper(
            env.halfDG.number_of_nodes(), env.partialw, env.partials,
            env.loc, env.dur, 6, env.num_robots, len(unsch_tasks)
        )
        
        feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) 
                       for k, v in feat_dict.items()}
        
        print("✓ Graph and features prepared")
        
        # Forward pass
        policy_net.train()
        outputs = policy_net(g, feat_tensors)
        q_values = outputs['value']
        
        print(f"✓ Forward pass successful: {q_values.shape}, dtype: {q_values.dtype}")
        
        # Create dummy targets
        num_actions = len(unsch_tasks)
        targets = torch.full((num_actions, 1), 1.0, device=device, dtype=torch.float32)
        weights = torch.full((num_actions, 1), 1.0, device=device, dtype=torch.float32)
        
        print(f"✓ Targets created: {targets.shape}, dtype: {targets.dtype}")
        
        # Compute loss
        loss = torch.nn.functional.mse_loss(q_values, targets, reduction='none')
        loss = (loss * weights).mean()
        
        print(f"✓ Loss computed: {loss.item():.6f}, dtype: {loss.dtype}")
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        print("✓ Backward pass successful!")
        print("✓ Simple training step completed without errors!")
        
        return True
        
    except Exception as e:
        print(f"✗ Error during training step test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("Training Data Types Test Suite")
    print("=" * 40)
    
    success1 = test_data_types()
    success2 = test_simple_training_step()
    
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    if success1:
        print("✓ Data types test: PASSED")
    else:
        print("✗ Data types test: FAILED")
    
    if success2:
        print("✓ Training step test: PASSED")
    else:
        print("✗ Training step test: FAILED")
    
    if success1 and success2:
        print("\n🎉 All tests passed! Training should work correctly now.")
        print("\nRun training with:")
        print("python3 multi_objective_train.py --path-to-train ./problem_instances/constraints --steps 100")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")


if __name__ == "__main__":
    main()
