import os
import torch
import argparse
import numpy as np
import pandas as pd

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import ScheduleNet4Layer
from multi_objective_utils import calculate_workload_balance

def load_model(model_path, device):
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'), ('task', 'located_in', 'loc'),
        ('loc', 'near', 'loc'), ('task', 'assigned_to', 'robot'),
        ('robot', 'com', 'robot'), ('task', 'tin', 'state'),
        ('loc', 'lin', 'state'), ('robot', 'rin', 'state'),
        ('state', 'sin', 'state'), ('task', 'tto', 'value'),
        ('robot', 'rto', 'value'), ('state', 'sto', 'value'),
        ('value', 'vto', 'value'), ('task', 'take_time', 'robot'),
        ('robot', 'use_time', 'task')
    ]
    model = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
    ckpt = torch.load(model_path, map_location=device)
    model.load_state_dict(ckpt['policy_net_state_dict'])
    model.eval()
    return model, ckpt.get('alpha', 0.5), ckpt.get('beta', 0.5)

def solve_instance(instance_prefix, num_tasks, num_robots, model, device):
    env = SchedulingEnv(instance_prefix)
    assignments = {r: [] for r in range(num_robots)}
    
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if not unsch_tasks:
            break
        best_score = -float('inf')
        best_r, best_t = None, None
        
        for r in range(num_robots):
            g = build_hetgraph(
                env.halfDG, num_tasks, num_robots, env.dur, 6,
                np.array(env.loc, dtype=np.int64), 1,
                env.partials, unsch_tasks, r, unsch_tasks
            ).to(device)
            feats = hetgraph_node_helper(
                env.halfDG.number_of_nodes(), env.partialw, env.partials,
                env.loc, env.dur, 6, num_robots, len(unsch_tasks)
            )
            feat_tensors = {k: torch.tensor(v, device=device).float() for k, v in feats.items()}
            with torch.no_grad():
                q_vals = model(g, feat_tensors)['value'].cpu().numpy().reshape(-1)
            idx = np.argmax(q_vals)
            if q_vals[idx] > best_score:
                best_score = q_vals[idx]
                best_t = int(unsch_tasks[idx])
                best_r = r
        
        success, _, done = env.insert_robot(best_t, best_r)
        if not success:
            return None, None, False
        assignments[best_r].append(best_t)
        if done:
            break

    _, makespan = env.check_consistency_makespan(updateDG=False)
    balance = calculate_workload_balance(assignments, num_robots)
    return makespan, balance, True

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--model-path', required=True)
    parser.add_argument('--test-data', required=True)
    parser.add_argument('--num-tasks', type=int, required=True)
    parser.add_argument('--num-robots', type=int, required=True)
    parser.add_argument('--max-instances', type=int, default=50)
    parser.add_argument('--device', default='cpu')
    parser.add_argument('--save-csv', default=None)
    args = parser.parse_args()

    device = torch.device(args.device)
    model, alpha, beta = load_model(args.model_path, device)
    print(f"Model loaded. Alpha: {alpha}, Beta: {beta}\n")

    records = []
    for i in range(1, args.max_instances + 1):
        prefix = os.path.join(args.test_data, f"{i:05d}")
        dur_path = f"{prefix}_dur.txt"
        if not os.path.isfile(dur_path):
            print(f"[{i:05d}] Missing file. Skipping.")
            continue
        print(f"[{i:05d}] Solving...")
        mk, wb, ok = solve_instance(prefix, args.num_tasks, args.num_robots, model, device)
        print(f"  Makespan: {mk:.2f}, Workload Balance: {wb:.3f}, Feasible: {ok}")
        records.append({'instance_id': i, 'makespan': mk, 'workload_balance': wb, 'feasible': ok})

    df = pd.DataFrame(records)
    print("\n=== Summary ===")
    print(df[df.feasible == True][['makespan', 'workload_balance']].describe())

    if args.save_csv:
        df.to_csv(args.save_csv, index=False)
        print(f"Results saved to: {args.save_csv}")

if __name__ == "__main__":
    main()

