#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_multi_objective_model.py

Comprehensive testing script for multi-objective trained models.
Tests both makespan and workload balance performance.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import ScheduleNet4Layer
from multi_objective_utils import (MultiObjectiveMetrics, calculate_workload_balance,
                                  calculate_workload_balance_from_env)
from makespan_for_each import solve_with_edf, solve_with_tercio, solve_with_random


def load_multi_objective_model(checkpoint_path: str, device: torch.device):
    """Load a trained multi-objective model from checkpoint."""
    # Network architecture (should match training)
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
    policy_net.eval()
    
    # Extract training parameters
    alpha = checkpoint.get('alpha', 0.5)
    beta = checkpoint.get('beta', 0.5)
    
    return policy_net, alpha, beta


def solve_with_multi_objective_ssan(prefix: str, num_tasks: int, num_robots: int, 
                                   policy_net, device: torch.device, 
                                   alpha: float = 0.5, beta: float = 0.5) -> Tuple[float, float, Dict, float, bool]:
    """
    Solve using multi-objective trained SSAN model.
    
    Returns:
        Tuple of (makespan, workload_balance, assignments, runtime, feasible_flag)
    """
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
        env.set_multi_objective_params(alpha=alpha, beta=beta)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception as e:
        print(f"Error loading environment: {e}")
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    assignment_order = []
    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True
    
    map_width = 6
    loc_dist_threshold = 1
    
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break
        
        best_r, best_t, best_q = None, None, -float("inf")
        
        # Evaluate each robot choice
        for r in range(num_robots):
            try:
                # Build heterogeneous graph
                g = build_hetgraph(
                    env.halfDG,
                    num_tasks,
                    num_robots,
                    env.dur,
                    map_width,
                    np.array(env.loc, dtype=np.int64),
                    loc_dist_threshold,
                    env.partials,
                    unsch_tasks,
                    r,
                    unsch_tasks
                ).to(device)
                
                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(),
                    env.partialw,
                    env.partials,
                    env.loc,
                    env.dur,
                    map_width,
                    num_robots,
                    len(unsch_tasks)
                )
                
                feat_tensors = {k: torch.tensor(v, device=device).float() for k, v in feat_dict.items()}
                
                # Forward pass
                with torch.no_grad():
                    outputs = policy_net(g, feat_tensors)
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                
                # Find best task for this robot
                idx = np.argmax(q_values)
                if q_values[idx] > best_q:
                    best_q = float(q_values[idx])
                    best_r = r
                    best_t = int(unsch_tasks[idx])
                    
            except Exception as e:
                print(f"Error evaluating robot {r}: {e}")
                continue
        
        if best_r is None or best_t is None:
            feasible_flag = False
            break
        
        # Execute best action
        success, _, done_flag = env.insert_robot(best_t, best_r)
        if not success:
            feasible_flag = False
            break
        
        assignment_order.append((best_t, best_r))
        assignments[best_r].append(best_t)
        
        if done_flag:
            break
    
    runtime = time.time() - t0
    
    if not feasible_flag:
        return float("nan"), float("nan"), {}, runtime, False
    
    # Calculate final metrics
    ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
    if not ok_final:
        return float("nan"), float("nan"), {}, runtime, False
    
    workload_balance = calculate_workload_balance(assignments, num_robots)
    
    return final_makespan, workload_balance, assignments, runtime, True


def evaluate_model_on_instances(model_path: str, test_data_path: str, 
                               num_tasks: int, num_robots: int, 
                               max_instances: int, device: torch.device) -> pd.DataFrame:
    """
    Evaluate multi-objective model on test instances.
    
    Returns:
        DataFrame with results for each instance and solver
    """
    print(f"Loading model from: {model_path}")
    policy_net, alpha, beta = load_multi_objective_model(model_path, device)
    print(f"Model trained with α={alpha:.3f}, β={beta:.3f}")
    
    results = []
    metrics = MultiObjectiveMetrics()
    
    for inst_id in range(1, max_instances + 1):
        prefix = os.path.join(test_data_path, f"{inst_id:05d}")
        
        # Check if instance exists
        if not os.path.isfile(f"{prefix}_dur.txt"):
            print(f"[Instance {inst_id:05d}] Files not found, skipping")
            continue
        
        print(f"[Instance {inst_id:05d}] Evaluating...")
        
        # Test different solvers
        solvers = {
            'multi_obj_ssan': lambda: solve_with_multi_objective_ssan(
                prefix, num_tasks, num_robots, policy_net, device, alpha, beta
            ),
            'edf': lambda: solve_with_edf(prefix, num_tasks, num_robots),
            'tercio': lambda: solve_with_tercio(prefix, num_tasks, num_robots),
            'random': lambda: solve_with_random(prefix, num_tasks, num_robots)
        }
        
        for solver_name, solver_func in solvers.items():
            try:
                if solver_name == 'multi_obj_ssan':
                    mk, balance, assigns, rt, ok = solver_func()
                else:
                    # For baseline solvers, calculate workload balance separately
                    try:
                        mk, assigns, rt, ok = solver_func()
                        if ok and isinstance(assigns, (dict, list)):
                            if isinstance(assigns, list):
                                # Convert assignment order to assignment dict
                                assign_dict = {r: [] for r in range(num_robots)}
                                for task, robot in assigns:
                                    assign_dict[robot].append(task)
                                assigns = assign_dict
                            balance = calculate_workload_balance(assigns, num_robots)
                        else:
                            balance = float('nan')
                    except AttributeError as e:
                        print(f"  {solver_name:15s}: AttributeError - {e}")
                        mk, balance, rt, ok = float('nan'), float('nan'), 0.0, False
                
                # Store results
                result = {
                    'instance_id': f"{inst_id:05d}",
                    'solver': solver_name,
                    'makespan': mk,
                    'workload_balance': balance,
                    'runtime': rt,
                    'feasible': ok
                }
                results.append(result)
                
                # Add to metrics if feasible
                if ok and not np.isnan(mk) and not np.isnan(balance):
                    metrics.add_solution(mk, balance, {'solver': solver_name, 'instance': inst_id})
                
                print(f"  {solver_name:15s}: makespan={mk:6.2f}, balance={balance:6.3f}, "
                      f"feasible={ok}, time={rt:.2f}s")
                
            except Exception as e:
                print(f"  {solver_name:15s}: ERROR - {e}")
                result = {
                    'instance_id': f"{inst_id:05d}",
                    'solver': solver_name,
                    'makespan': float('nan'),
                    'workload_balance': float('nan'),
                    'runtime': 0.0,
                    'feasible': False
                }
                results.append(result)
    
    return pd.DataFrame(results), metrics


def analyze_results(df: pd.DataFrame, metrics: MultiObjectiveMetrics, output_dir: str):
    """Analyze and visualize results."""
    print("\n=== ANALYSIS RESULTS ===")

    # Check if we have any data
    if df.empty:
        print("No data to analyze - no instances were successfully processed.")
        return

    # Summary statistics per solver
    summary_stats = []
    for solver in df['solver'].unique():
        solver_data = df[df['solver'] == solver]
        feasible_data = solver_data[solver_data['feasible'] == True]
        
        if len(feasible_data) > 0:
            stats = {
                'solver': solver,
                'num_feasible': len(feasible_data),
                'num_total': len(solver_data),
                'feasibility_rate': len(feasible_data) / len(solver_data),
                'avg_makespan': feasible_data['makespan'].mean(),
                'std_makespan': feasible_data['makespan'].std(),
                'avg_workload_balance': feasible_data['workload_balance'].mean(),
                'std_workload_balance': feasible_data['workload_balance'].std(),
                'avg_runtime': feasible_data['runtime'].mean()
            }
        else:
            stats = {
                'solver': solver,
                'num_feasible': 0,
                'num_total': len(solver_data),
                'feasibility_rate': 0.0,
                'avg_makespan': float('nan'),
                'std_makespan': float('nan'),
                'avg_workload_balance': float('nan'),
                'std_workload_balance': float('nan'),
                'avg_runtime': solver_data['runtime'].mean()
            }
        summary_stats.append(stats)
    
    summary_df = pd.DataFrame(summary_stats)
    print("\nSolver Performance Summary:")
    print(summary_df.to_string(index=False, float_format='%.3f'))
    
    # Save detailed results
    os.makedirs(output_dir, exist_ok=True)
    df.to_csv(os.path.join(output_dir, 'detailed_results.csv'), index=False)
    summary_df.to_csv(os.path.join(output_dir, 'summary_statistics.csv'), index=False)
    
    # Generate Pareto front plot
    pareto_plot_path = os.path.join(output_dir, 'pareto_front.png')
    metrics.plot_pareto_front(save_path=pareto_plot_path)
    
    # Generate comparison plots
    feasible_df = df[df['feasible'] == True]
    if len(feasible_df) > 0:
        try:
            # Makespan comparison
            plt.figure(figsize=(12, 5))

            plt.subplot(1, 2, 1)
            for solver in feasible_df['solver'].unique():
                solver_data = feasible_df[feasible_df['solver'] == solver]
                plt.scatter(range(len(solver_data)), solver_data['makespan'],
                           label=solver, alpha=0.7)
            plt.xlabel('Instance')
            plt.ylabel('Makespan')
            plt.title('Makespan Comparison')
            plt.legend()
            plt.grid(True, alpha=0.3)

            # Workload balance comparison
            plt.subplot(1, 2, 2)
            for solver in feasible_df['solver'].unique():
                solver_data = feasible_df[feasible_df['solver'] == solver]
                plt.scatter(range(len(solver_data)), solver_data['workload_balance'],
                           label=solver, alpha=0.7)
            plt.xlabel('Instance')
            plt.ylabel('Workload Balance')
            plt.title('Workload Balance Comparison')
            plt.legend()
            plt.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'comparison_plots.png'), dpi=300, bbox_inches='tight')
            plt.close()
            print("Comparison plots saved successfully")
        except Exception as e:
            print(f"Warning: Could not generate comparison plots: {e}")
    
    print(f"\nResults saved to: {output_dir}")
    
    # Print key insights
    print("\n=== KEY INSIGHTS ===")
    if 'multi_obj_ssan' in summary_df['solver'].values:
        ssan_stats = summary_df[summary_df['solver'] == 'multi_obj_ssan'].iloc[0]
        print(f"Multi-objective SSAN:")
        print(f"  - Feasibility rate: {ssan_stats['feasibility_rate']:.1%}")
        print(f"  - Average makespan: {ssan_stats['avg_makespan']:.2f}")
        print(f"  - Average workload balance: {ssan_stats['avg_workload_balance']:.3f}")
        print(f"  - Average runtime: {ssan_stats['avg_runtime']:.3f}s")
    
    pareto_front = metrics.get_pareto_front()
    print(f"\nPareto front contains {len(pareto_front)} solutions")
    
    overall_stats = metrics.get_statistics()
    if overall_stats:
        print(f"Total solutions evaluated: {overall_stats['total_solutions']}")


def main():
    parser = argparse.ArgumentParser(description="Test multi-objective trained model")
    parser.add_argument("--model-path", default='./cp_multi_obj/checkpoint_00500.tar' ,required=True, help="Path to trained model checkpoint")
    parser.add_argument("--test-data",default="./problem_instances/constraints", required=True, help="Path to test data directory")
    parser.add_argument("--num-tasks", default=5,type=int, required=True, help="Number of tasks")
    parser.add_argument("--num-robots",default=2, type=int, required=True, help="Number of robots")
    parser.add_argument("--max-instances", type=int, default=50, help="Maximum instances to test")
    parser.add_argument("--output-dir", default="./test_results", help="Output directory")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    
    args = parser.parse_args()
    
    device = torch.device(args.device)
    
    print("Multi-Objective Model Testing")
    print("=" * 40)
    print(f"Model: {args.model_path}")
    print(f"Test data: {args.test_data}")
    print(f"Problem size: {args.num_tasks} tasks, {args.num_robots} robots")
    print(f"Max instances: {args.max_instances}")
    
    # Run evaluation
    results_df, metrics = evaluate_model_on_instances(
        args.model_path, args.test_data, args.num_tasks, args.num_robots,
        args.max_instances, device
    )
    
    # Analyze results
    analyze_results(results_df, metrics, args.output_dir)
    
    print("\nTesting completed!")


if __name__ == "__main__":
    main()
