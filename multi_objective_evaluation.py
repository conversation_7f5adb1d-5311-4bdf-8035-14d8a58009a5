#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
multi_objective_evaluation.py

Comprehensive evaluation script for multi-objective MRC scheduling.
Evaluates both makespan and workload balance objectives across different solvers.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from utils import SchedulingEnv
from workload_balance_solvers import (solve_workload_balance_gurobi, 
                                     solve_multi_objective_gurobi,
                                     solve_workload_balance_heuristic)
from multi_objective_utils import (MultiObjectiveMetrics, calculate_workload_balance,
                                  normalize_objectives)
from makespan_for_each import (solve_with_edf, solve_with_tercio, 
                              solve_with_random, solve_with_gurobi)


def evaluate_instance_multi_objective(prefix: str, num_tasks: int, num_robots: int) -> Dict:
    """
    Evaluate a single instance with multiple solvers for multi-objective comparison.
    
    Returns:
        Dict with results for each solver: {solver_name: (makespan, workload_balance, runtime, feasible)}
    """
    results = {}
    
    # 1. Makespan-only solvers
    solvers_makespan = {
        'gurobi_makespan': solve_with_gurobi,
        'edf': solve_with_edf,
        'tercio': solve_with_tercio,
        'random': solve_with_random
    }
    
    for solver_name, solver_func in solvers_makespan.items():
        try:
            mk, assigns, rt, ok = solver_func(prefix, num_tasks, num_robots)
            if ok and isinstance(assigns, dict):
                # Calculate workload balance from assignments
                balance = calculate_workload_balance(assigns, num_robots)
                results[solver_name] = (mk, balance, rt, ok)
            else:
                results[solver_name] = (float('nan'), float('nan'), rt, False)
        except Exception as e:
            print(f"Error in {solver_name}: {e}")
            results[solver_name] = (float('nan'), float('nan'), 0.0, False)
    
    # 2. Workload balance-only solver
    try:
        balance_score, assigns, rt, ok = solve_workload_balance_gurobi(prefix, num_tasks, num_robots)
        if ok:
            # Calculate makespan for this assignment
            env = SchedulingEnv(prefix)
            for robot_id, tasks in assigns.items():
                for task in tasks:
                    env.insert_robot(task, robot_id)
            _, makespan = env.check_consistency_makespan(updateDG=False)
            results['gurobi_balance'] = (makespan, balance_score, rt, ok)
        else:
            results['gurobi_balance'] = (float('nan'), float('nan'), rt, False)
    except Exception as e:
        print(f"Error in workload balance solver: {e}")
        results['gurobi_balance'] = (float('nan'), float('nan'), 0.0, False)
    
    # 3. Multi-objective solvers with different weight combinations
    weight_combinations = [
        (0.8, 0.2, 'multi_obj_80_20'),  # Favor makespan
        (0.5, 0.5, 'multi_obj_50_50'),  # Equal weights
        (0.2, 0.8, 'multi_obj_20_80'),  # Favor workload balance
    ]
    
    for alpha, beta, solver_name in weight_combinations:
        try:
            mk, balance, assigns, rt, ok = solve_multi_objective_gurobi(
                prefix, num_tasks, num_robots, alpha, beta
            )
            results[solver_name] = (mk, balance, rt, ok)
        except Exception as e:
            print(f"Error in {solver_name}: {e}")
            results[solver_name] = (float('nan'), float('nan'), 0.0, False)
    
    # 4. Heuristic workload balance solver
    try:
        balance_score, assigns, rt, ok = solve_workload_balance_heuristic(prefix, num_tasks, num_robots)
        if ok:
            # Calculate makespan for this assignment
            env = SchedulingEnv(prefix)
            for robot_id, tasks in assigns.items():
                for task in tasks:
                    env.insert_robot(task, robot_id)
            _, makespan = env.check_consistency_makespan(updateDG=False)
            results['balance_heuristic'] = (makespan, balance_score, rt, ok)
        else:
            results['balance_heuristic'] = (float('nan'), float('nan'), rt, False)
    except Exception as e:
        print(f"Error in balance heuristic solver: {e}")
        results['balance_heuristic'] = (float('nan'), float('nan'), 0.0, False)
    
    return results


def main():
    parser = argparse.ArgumentParser(description="Multi-objective MRC scheduling evaluation")
    parser.add_argument("--path-test-data", required=True,
                       help="Path to test constraints folder")
    parser.add_argument("--num-tasks", type=int, required=True,
                       help="Number of tasks per instance")
    parser.add_argument("--num-robots", type=int, required=True,
                       help="Number of robots per instance")
    parser.add_argument("--max-instances", type=int, required=True,
                       help="Maximum number of instances to evaluate")
    parser.add_argument("--output-folder", required=True,
                       help="Folder to save results")
    
    args = parser.parse_args()
    
    os.makedirs(args.output_folder, exist_ok=True)
    
    # Initialize metrics tracker
    metrics = MultiObjectiveMetrics()
    
    # Results storage
    all_results = []
    
    print(f"Starting multi-objective evaluation...")
    print(f"Tasks: {args.num_tasks}, Robots: {args.num_robots}")
    print(f"Max instances: {args.max_instances}")
    
    for inst_id in range(1, args.max_instances + 1):
        prefix = os.path.join(args.path_test_data, f"{inst_id:05d}")
        
        # Check if instance files exist
        if not os.path.isfile(f"{prefix}_dur.txt"):
            print(f"[Instance {inst_id:05d}] No constraint files found, skipping.")
            continue
        
        print(f"[Instance {inst_id:05d}] Evaluating...")
        
        # Evaluate instance with all solvers
        instance_results = evaluate_instance_multi_objective(prefix, args.num_tasks, args.num_robots)
        
        # Store results
        for solver_name, (makespan, balance, runtime, feasible) in instance_results.items():
            result_row = {
                'instance_id': f"{inst_id:05d}",
                'solver': solver_name,
                'makespan': makespan,
                'workload_balance': balance,
                'runtime': runtime,
                'feasible': feasible
            }
            all_results.append(result_row)
            
            # Add to metrics tracker if feasible
            if feasible and not np.isnan(makespan) and not np.isnan(balance):
                metrics.add_solution(makespan, balance, {'solver': solver_name, 'instance': inst_id})
        
        print(f"[Instance {inst_id:05d}] Completed")
    
    # Save detailed results to CSV
    df_results = pd.DataFrame(all_results)
    csv_path = os.path.join(args.output_folder, "multi_objective_results.csv")
    df_results.to_csv(csv_path, index=False)
    print(f"Detailed results saved to: {csv_path}")
    
    # Generate summary statistics
    summary_stats = []
    for solver in df_results['solver'].unique():
        solver_data = df_results[df_results['solver'] == solver]
        feasible_data = solver_data[solver_data['feasible'] == True]
        
        if len(feasible_data) > 0:
            stats = {
                'solver': solver,
                'num_feasible': len(feasible_data),
                'num_total': len(solver_data),
                'feasibility_rate': len(feasible_data) / len(solver_data),
                'avg_makespan': feasible_data['makespan'].mean(),
                'std_makespan': feasible_data['makespan'].std(),
                'avg_workload_balance': feasible_data['workload_balance'].mean(),
                'std_workload_balance': feasible_data['workload_balance'].std(),
                'avg_runtime': feasible_data['runtime'].mean()
            }
        else:
            stats = {
                'solver': solver,
                'num_feasible': 0,
                'num_total': len(solver_data),
                'feasibility_rate': 0.0,
                'avg_makespan': float('nan'),
                'std_makespan': float('nan'),
                'avg_workload_balance': float('nan'),
                'std_workload_balance': float('nan'),
                'avg_runtime': solver_data['runtime'].mean()
            }
        summary_stats.append(stats)
    
    # Save summary statistics
    df_summary = pd.DataFrame(summary_stats)
    summary_path = os.path.join(args.output_folder, "summary_statistics.csv")
    df_summary.to_csv(summary_path, index=False)
    print(f"Summary statistics saved to: {summary_path}")
    
    # Generate Pareto front plot
    pareto_plot_path = os.path.join(args.output_folder, "pareto_front.png")
    metrics.plot_pareto_front(save_path=pareto_plot_path)
    
    # Print overall statistics
    overall_stats = metrics.get_statistics()
    print("\n=== OVERALL STATISTICS ===")
    print(f"Total solutions: {overall_stats.get('total_solutions', 0)}")
    print(f"Pareto front size: {overall_stats.get('pareto_front_size', 0)}")
    
    if 'makespan_stats' in overall_stats:
        mk_stats = overall_stats['makespan_stats']
        print(f"Makespan - Min: {mk_stats['min']:.2f}, Max: {mk_stats['max']:.2f}, Mean: {mk_stats['mean']:.2f}")
    
    if 'balance_stats' in overall_stats:
        bal_stats = overall_stats['balance_stats']
        print(f"Workload Balance - Min: {bal_stats['min']:.2f}, Max: {bal_stats['max']:.2f}, Mean: {bal_stats['mean']:.2f}")
    
    print(f"\nEvaluation completed. Results saved to: {args.output_folder}")


if __name__ == "__main__":
    main()
