#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_models_csv_format.py

Test models and save results in the exact CSV format matching csv_outputs folder structure.
Each model gets separate CSV files for makespan and workload balance results.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Tuple

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import ScheduleNet4Layer
from multi_objective_utils import calculate_workload_balance


def load_model(checkpoint_path: str, device: torch.device):
    """Load the trained model from checkpoint."""
    # Network architecture
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
    policy_net.eval()
    
    # Extract training parameters
    alpha = checkpoint.get('alpha', 0.5)
    beta = checkpoint.get('beta', 0.5)
    step = checkpoint.get('step', 0)
    
    return policy_net, alpha, beta, step


def solve_with_multi_objective_model(prefix: str, num_tasks: int, num_robots: int, 
                                    policy_net, device: torch.device, 
                                    alpha: float = 0.5, beta: float = 0.5) -> Tuple[float, float, Dict, float, bool]:
    """Solve using multi-objective trained model."""
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
        env.set_multi_objective_params(alpha=alpha, beta=beta)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception as e:
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True
    
    map_width = 6
    loc_dist_threshold = 1
    
    step_count = 0
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break
        
        step_count += 1
        if step_count > num_tasks * 2:  # Safety check
            feasible_flag = False
            break
        
        best_r, best_t, best_q = None, None, -float("inf")
        
        # Evaluate each robot choice
        for r in range(num_robots):
            try:
                # Build heterogeneous graph with consistent data types
                g = build_hetgraph(
                    env.halfDG,
                    num_tasks,
                    num_robots,
                    env.dur.astype(np.float32),
                    map_width,
                    np.array(env.loc, dtype=np.int64),
                    loc_dist_threshold,
                    env.partials,
                    np.array(unsch_tasks, dtype=np.int64),  # Ensure int64
                    r,
                    np.array(unsch_tasks, dtype=np.int64)   # Ensure int64
                ).to(device)
                
                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(),
                    env.partialw,
                    env.partials,
                    env.loc,
                    env.dur,
                    map_width,
                    num_robots,
                    len(unsch_tasks)
                )
                
                feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) 
                               for k, v in feat_dict.items()}
                
                # Forward pass
                with torch.no_grad():
                    outputs = policy_net(g, feat_tensors)
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                
                # Find best task for this robot
                idx = np.argmax(q_values)
                if q_values[idx] > best_q:
                    best_q = float(q_values[idx])
                    best_r = r
                    best_t = int(unsch_tasks[idx])
                    
            except Exception as e:
                continue
        
        if best_r is None or best_t is None:
            feasible_flag = False
            break
        
        # Execute best action
        success, _, done_flag = env.insert_robot(best_t, best_r)
        if not success:
            feasible_flag = False
            break
        
        assignments[best_r].append(best_t)
        
        if done_flag:
            break
    
    runtime = time.time() - t0
    
    if not feasible_flag:
        return float("nan"), float("nan"), assignments, runtime, False
    
    # Calculate final metrics
    try:
        ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
        if not ok_final:
            return float("nan"), float("nan"), assignments, runtime, False
        
        workload_balance = calculate_workload_balance(assignments, num_robots)
        
        return final_makespan, workload_balance, assignments, runtime, True
        
    except Exception as e:
        return float("nan"), float("nan"), assignments, runtime, False


def solve_with_baseline(prefix: str, num_tasks: int, num_robots: int, 
                       strategy: str = "random") -> Tuple[float, float, Dict, float, bool]:
    """Solve using baseline strategy."""
    t0 = time.time()
    
    try:
        # Use standard environment (no multi-objective) for baseline
        env = SchedulingEnv(prefix)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception as e:
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True
    
    step_count = 0
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break
        
        step_count += 1
        if step_count > num_tasks * 2:  # Safety check
            feasible_flag = False
            break
        
        # Choose task and robot based on strategy
        try:
            if strategy == "random":
                task = np.random.choice(unsch_tasks)
                robot = np.random.randint(0, num_robots)
            elif strategy == "round_robin":
                task = unsch_tasks[0]  # First available task
                robot = step_count % num_robots  # Round-robin robot assignment
            elif strategy == "greedy_duration":
                # Choose task-robot pair with minimum duration
                min_dur = float('inf')
                best_task, best_robot = unsch_tasks[0], 0
                for t in unsch_tasks:
                    for r in range(num_robots):
                        dur = env.dur[t-1, r]
                        if dur < min_dur:
                            min_dur = dur
                            best_task, best_robot = t, r
                task, robot = best_task, best_robot
            elif strategy == "greedy_balance":
                # Choose robot with fewest assigned tasks
                task = unsch_tasks[0]  # First available task
                task_counts = [len(assignments[r]) for r in range(num_robots)]
                robot = np.argmin(task_counts)
            else:
                # Default to random
                task = np.random.choice(unsch_tasks)
                robot = np.random.randint(0, num_robots)
        except Exception as e:
            task = unsch_tasks[0]
            robot = 0
        
        # Execute action
        success, _, done_flag = env.insert_robot(task, robot)
        if not success:
            feasible_flag = False
            break
        
        assignments[robot].append(task)
        
        if done_flag:
            break
    
    runtime = time.time() - t0
    
    if not feasible_flag:
        return float("nan"), float("nan"), assignments, runtime, False
    
    # Calculate final metrics
    try:
        ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
        if not ok_final:
            return float("nan"), float("nan"), assignments, runtime, False
        
        workload_balance = calculate_workload_balance(assignments, num_robots)
        
        return final_makespan, workload_balance, assignments, runtime, True
        
    except Exception as e:
        return float("nan"), float("nan"), assignments, runtime, False


def test_model_and_save_csv(model_info: Dict, test_data_path: str, num_tasks: int, 
                           num_robots: int, max_instances: int, device: torch.device, 
                           output_dir: str):
    """Test a model and save results in CSV format matching csv_outputs structure."""
    model_name = model_info['name']
    print(f"\n=== Testing {model_name} ===")
    
    # Prepare results storage
    makespan_results = []
    balance_results = []
    
    successful_instances = 0
    total_instances = 0
    
    for inst_id in range(1, max_instances + 1):
        prefix = os.path.join(test_data_path, f"{inst_id:05d}")
        
        # Check if instance exists
        if not os.path.isfile(f"{prefix}_dur.txt"):
            continue
        
        total_instances += 1
        print(f"[Instance {inst_id:05d}] Testing {model_name}...")
        
        # Test the model
        if model_info['type'] == 'multi_objective':
            mk, balance, assigns, rt, ok = solve_with_multi_objective_model(
                prefix, num_tasks, num_robots, 
                model_info['policy_net'], device, 
                model_info['alpha'], model_info['beta']
            )
        elif model_info['type'] == 'baseline':
            mk, balance, assigns, rt, ok = solve_with_baseline(
                prefix, num_tasks, num_robots, model_info['strategy']
            )
        else:
            print(f"Unknown model type: {model_info['type']}")
            continue
        
        # Format results to match csv_outputs structure
        instance_id_str = f"{inst_id:05d}"
        
        # Makespan result
        makespan_result = {
            'instance_id': instance_id_str,
            'makespan': mk if ok else float('nan'),
            'feasible': 1 if ok else 0,
            'runtime': rt
        }
        makespan_results.append(makespan_result)
        
        # Workload balance result
        balance_result = {
            'instance_id': instance_id_str,
            'workload_balance': balance if ok else float('nan'),
            'feasible': 1 if ok else 0,
            'runtime': rt
        }
        balance_results.append(balance_result)
        
        if ok:
            successful_instances += 1
            print(f"  ✓ makespan={mk:.1f}, balance={balance:.3f}, time={rt:.3f}s")
        else:
            print(f"  ✗ infeasible, time={rt:.3f}s")
    
    # Save CSV files
    if makespan_results:
        # Save makespan results
        makespan_df = pd.DataFrame(makespan_results)
        makespan_filename = f"makespans_{model_name.lower().replace(' ', '_')}.csv"
        makespan_path = os.path.join(output_dir, makespan_filename)
        makespan_df.to_csv(makespan_path, index=False)
        
        # Save workload balance results
        balance_df = pd.DataFrame(balance_results)
        balance_filename = f"workload_balance_{model_name.lower().replace(' ', '_')}.csv"
        balance_path = os.path.join(output_dir, balance_filename)
        balance_df.to_csv(balance_path, index=False)
        
        # Print summary
        print(f"\n{model_name} Summary:")
        print(f"  Instances tested: {total_instances}")
        print(f"  Feasible solutions: {successful_instances}/{total_instances} ({successful_instances/total_instances*100:.1f}%)")
        
        if successful_instances > 0:
            feasible_makespan = makespan_df[makespan_df['feasible'] == 1]
            feasible_balance = balance_df[balance_df['feasible'] == 1]
            
            avg_makespan = feasible_makespan['makespan'].mean()
            avg_balance = feasible_balance['workload_balance'].mean()
            avg_runtime = feasible_makespan['runtime'].mean()
            
            print(f"  Average makespan: {avg_makespan:.2f}")
            print(f"  Average workload balance: {avg_balance:.3f}")
            print(f"  Average runtime: {avg_runtime:.3f}s")
        
        print(f"  Makespan results saved to: {makespan_path}")
        print(f"  Workload balance results saved to: {balance_path}")
        
        return makespan_path, balance_path
    else:
        print(f"No results for {model_name}")
        return None, None


def main():
    parser = argparse.ArgumentParser(description="Test models and save in csv_outputs format")
    parser.add_argument("--model-path", help="Path to trained model checkpoint")
    parser.add_argument("--test-data", default="./problem_instances_test/constraints",
                       help="Path to test data directory")
    parser.add_argument("--num-tasks", default=5, type=int, help="Number of tasks")
    parser.add_argument("--num-robots", default=2, type=int, help="Number of robots")
    parser.add_argument("--max-instances", type=int, default=50, help="Maximum instances to test")
    parser.add_argument("--output-dir", default="./csv_outputs_multi_objective", help="Output directory")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    parser.add_argument("--test-baselines", action="store_true", help="Also test baseline methods")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducible results")
    
    args = parser.parse_args()

    # Set random seed for reproducible results
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)

    device = torch.device(args.device)
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("Multi-Objective Model Testing - CSV Format")
    print("=" * 50)
    print(f"Test data: {args.test_data}")
    print(f"Problem size: {args.num_tasks} tasks, {args.num_robots} robots")
    print(f"Max instances: {args.max_instances}")
    print(f"Output directory: {args.output_dir}")
    
    models_to_test = []
    
    # Add multi-objective model if provided
    if args.model_path and os.path.isfile(args.model_path):
        try:
            policy_net, alpha, beta, step = load_model(args.model_path, device)
            models_to_test.append({
                'name': f'multi_obj_a{alpha:.1f}_b{beta:.1f}',
                'type': 'multi_objective',
                'policy_net': policy_net,
                'alpha': alpha,
                'beta': beta,
                'step': step
            })
            print(f"✓ Loaded multi-objective model: α={alpha:.3f}, β={beta:.3f}, step={step}")
        except Exception as e:
            print(f"✗ Failed to load model {args.model_path}: {e}")
    
    # Add baseline models if requested
    if args.test_baselines:
        baseline_strategies = ["random", "round_robin", "greedy_duration", "greedy_balance"]
        for strategy in baseline_strategies:
            models_to_test.append({
                'name': strategy,
                'type': 'baseline',
                'strategy': strategy
            })
        print(f"✓ Added {len(baseline_strategies)} baseline models")
    
    if not models_to_test:
        print("No models to test. Please provide --model-path or use --test-baselines")
        return
    
    # Test each model separately
    csv_files = []
    for model_info in models_to_test:
        makespan_csv, balance_csv = test_model_and_save_csv(
            model_info, args.test_data, args.num_tasks, args.num_robots,
            args.max_instances, device, args.output_dir
        )
        if makespan_csv:
            csv_files.extend([makespan_csv, balance_csv])
    
    # Save list of generated CSV files
    if csv_files:
        csv_list_path = os.path.join(args.output_dir, "generated_csv_files.txt")
        with open(csv_list_path, 'w') as f:
            for csv_file in csv_files:
                f.write(f"{csv_file}\n")
        
        print(f"\n=== TESTING COMPLETED ===")
        print(f"Generated {len(csv_files)} CSV files:")
        for csv_file in csv_files:
            print(f"  - {os.path.basename(csv_file)}")
        print(f"CSV file list saved to: {csv_list_path}")
        print(f"\nFiles saved in format matching csv_outputs structure")
    else:
        print("No CSV files generated.")


if __name__ == "__main__":
    main()
