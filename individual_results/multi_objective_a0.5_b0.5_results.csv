instance_id,model_name,model_type,makespan,workload_balance,runtime,feasible,details,num_tasks,num_robots,alpha,beta,training_step,robot_0_tasks,robot_0_count,robot_1_tasks,robot_1_count
00001,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0038270950317382812,<PERSON><PERSON>e,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00002,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0030181407928466797,<PERSON>alse,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00003,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0030524730682373047,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00004,Multi_Objective_a0.5_b0.5,multi_objective,,,0.002977132797241211,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00005,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0032453536987304688,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00006,Multi_Objective_a0.5_b0.5,multi_objective,,,0.003022432327270508,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00007,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0031404495239257812,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00008,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0032286643981933594,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00009,Multi_Objective_a0.5_b0.5,multi_objective,,,0.00311279296875,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00010,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0033652782440185547,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00011,Multi_Objective_a0.5_b0.5,multi_objective,,,0.003979206085205078,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00012,Multi_Objective_a0.5_b0.5,multi_objective,,,0.003597259521484375,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00013,Multi_Objective_a0.5_b0.5,multi_objective,,,0.003762483596801758,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00014,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0033655166625976562,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00015,Multi_Objective_a0.5_b0.5,multi_objective,,,0.003519773483276367,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00016,Multi_Objective_a0.5_b0.5,multi_objective,,,0.003443002700805664,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00017,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0035598278045654297,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00018,Multi_Objective_a0.5_b0.5,multi_objective,,,0.003306150436401367,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00019,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0035400390625,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
00020,Multi_Objective_a0.5_b0.5,multi_objective,,,0.0034711360931396484,False,"Robot 0 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; Robot 1 evaluation error: Expect the source and destination node IDs to have the same type, but got torch.int32 and torch.int64.; No valid robot-task assignment found",5,2,0.5,0.5,500,[],0,[],0
