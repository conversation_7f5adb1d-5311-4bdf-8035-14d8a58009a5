#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
plot_training_curves.py

Standalone script to plot training curves from saved metrics or checkpoint files.
Can be used to analyze training progress and compare different models.
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
from typing import List, Tuple, Optional


def extract_metrics_from_checkpoint(checkpoint_path: str) -> Tuple[List[int], List[float], Optional[float], Optional[float]]:
    """
    Extract training metrics from a checkpoint file.
    
    Args:
        checkpoint_path: Path to the checkpoint file
        
    Returns:
        Tuple of (steps, losses, alpha, beta)
    """
    try:
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # Extract basic info
        step = checkpoint.get('step', 0)
        loss = checkpoint.get('loss', 0.0)
        alpha = checkpoint.get('alpha', None)
        beta = checkpoint.get('beta', None)
        
        # For single checkpoint, create minimal data
        steps = [step] if step > 0 else [1]
        losses = [loss] if loss > 0 else [0.0]
        
        return steps, losses, alpha, beta
        
    except Exception as e:
        print(f"Error loading checkpoint {checkpoint_path}: {e}")
        return [], [], None, None


def load_metrics_from_csv(csv_path: str) -> Tuple[List[int], List[float], List[float]]:
    """
    Load training metrics from CSV file.
    
    Args:
        csv_path: Path to the CSV file
        
    Returns:
        Tuple of (steps, losses, rewards)
    """
    try:
        df = pd.read_csv(csv_path)
        steps = df['step'].tolist()
        losses = df['loss'].tolist()
        rewards = df.get('avg_reward', [0.0] * len(steps)).tolist()
        
        return steps, losses, rewards
        
    except Exception as e:
        print(f"Error loading CSV {csv_path}: {e}")
        return [], [], []


def plot_single_training_curve(steps: List[int], losses: List[float], rewards: List[float],
                              title: str = "Training Curves", save_path: Optional[str] = None):
    """
    Plot training curves for a single model.
    
    Args:
        steps: Training steps
        losses: Training losses
        rewards: Average rewards
        title: Plot title
        save_path: Path to save the plot
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot training loss
    if losses:
        ax1.plot(steps, losses, 'b-', linewidth=2, label='Training Loss')
        ax1.set_xlabel('Training Step')
        ax1.set_ylabel('Loss')
        ax1.set_title(f'{title} - Loss')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Add trend line
        if len(steps) > 5:
            z = np.polyfit(steps, losses, 1)
            p = np.poly1d(z)
            ax1.plot(steps, p(steps), 'r--', alpha=0.7, label=f'Trend (slope={z[0]:.6f})')
            ax1.legend()
    else:
        ax1.text(0.5, 0.5, 'No loss data available', ha='center', va='center', transform=ax1.transAxes)
        ax1.set_title(f'{title} - Loss (No Data)')
    
    # Plot average reward
    if rewards and any(r != 0 for r in rewards):
        ax2.plot(steps, rewards, 'g-', linewidth=2, label='Average Reward')
        ax2.set_xlabel('Training Step')
        ax2.set_ylabel('Average Reward')
        ax2.set_title(f'{title} - Reward')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Add trend line
        if len(steps) > 5:
            z = np.polyfit(steps, rewards, 1)
            p = np.poly1d(z)
            ax2.plot(steps, p(steps), 'r--', alpha=0.7, label=f'Trend (slope={z[0]:.6f})')
            ax2.legend()
    else:
        ax2.text(0.5, 0.5, 'No reward data available', ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title(f'{title} - Reward (No Data)')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to: {save_path}")
    
    plt.show()


def plot_comparison_curves(models_data: List[Tuple[str, List[int], List[float], List[float]]],
                          save_path: Optional[str] = None):
    """
    Plot comparison of multiple models' training curves.
    
    Args:
        models_data: List of (model_name, steps, losses, rewards) tuples
        save_path: Path to save the plot
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
    
    # Plot losses
    for i, (model_name, steps, losses, rewards) in enumerate(models_data):
        if losses:
            color = colors[i % len(colors)]
            ax1.plot(steps, losses, color=color, linewidth=2, label=model_name)
    
    ax1.set_xlabel('Training Step')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training Loss Comparison')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Plot rewards
    for i, (model_name, steps, losses, rewards) in enumerate(models_data):
        if rewards and any(r != 0 for r in rewards):
            color = colors[i % len(colors)]
            ax2.plot(steps, rewards, color=color, linewidth=2, label=model_name)
    
    ax2.set_xlabel('Training Step')
    ax2.set_ylabel('Average Reward')
    ax2.set_title('Average Reward Comparison')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Comparison plot saved to: {save_path}")
    
    plt.show()


def analyze_training_progress(steps: List[int], losses: List[float], rewards: List[float]) -> dict:
    """
    Analyze training progress and provide statistics.
    
    Args:
        steps: Training steps
        losses: Training losses
        rewards: Average rewards
        
    Returns:
        Dictionary with analysis results
    """
    analysis = {}
    
    if losses:
        analysis['loss_stats'] = {
            'initial_loss': losses[0],
            'final_loss': losses[-1],
            'min_loss': min(losses),
            'max_loss': max(losses),
            'loss_reduction': losses[0] - losses[-1] if len(losses) > 1 else 0,
            'loss_reduction_percent': ((losses[0] - losses[-1]) / losses[0] * 100) if len(losses) > 1 and losses[0] != 0 else 0
        }
        
        # Calculate convergence (last 10% of training)
        if len(losses) > 10:
            last_10_percent = int(len(losses) * 0.1)
            recent_losses = losses[-last_10_percent:]
            analysis['convergence'] = {
                'recent_loss_std': np.std(recent_losses),
                'recent_loss_mean': np.mean(recent_losses),
                'is_converging': np.std(recent_losses) < 0.1 * np.mean(recent_losses)
            }
    
    if rewards and any(r != 0 for r in rewards):
        analysis['reward_stats'] = {
            'initial_reward': rewards[0],
            'final_reward': rewards[-1],
            'max_reward': max(rewards),
            'min_reward': min(rewards),
            'reward_improvement': rewards[-1] - rewards[0] if len(rewards) > 1 else 0
        }
    
    return analysis


def main():
    parser = argparse.ArgumentParser(description="Plot training curves for multi-objective models")
    parser.add_argument("--checkpoint-dir", help="Directory containing checkpoint files")
    parser.add_argument("--checkpoint-file", help="Single checkpoint file to analyze")
    parser.add_argument("--csv-file", help="CSV file with training metrics")
    parser.add_argument("--output-dir", default="./plots", help="Output directory for plots")
    parser.add_argument("--compare", action="store_true", help="Compare multiple models")
    
    args = parser.parse_args()
    
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.csv_file:
        # Load from CSV file
        print(f"Loading metrics from CSV: {args.csv_file}")
        steps, losses, rewards = load_metrics_from_csv(args.csv_file)
        
        if steps:
            # Analyze training
            analysis = analyze_training_progress(steps, losses, rewards)
            print("\nTraining Analysis:")
            for key, value in analysis.items():
                print(f"{key}: {value}")
            
            # Plot curves
            title = f"Training Progress ({os.path.basename(args.csv_file)})"
            save_path = os.path.join(args.output_dir, "training_curves_from_csv.png")
            plot_single_training_curve(steps, losses, rewards, title, save_path)
        else:
            print("No data found in CSV file")
    
    elif args.checkpoint_file:
        # Load from single checkpoint
        print(f"Loading metrics from checkpoint: {args.checkpoint_file}")
        steps, losses, alpha, beta = extract_metrics_from_checkpoint(args.checkpoint_file)
        
        if steps:
            title = f"Checkpoint Analysis (α={alpha:.1f}, β={beta:.1f})" if alpha is not None else "Checkpoint Analysis"
            save_path = os.path.join(args.output_dir, "checkpoint_analysis.png")
            plot_single_training_curve(steps, losses, [0] * len(steps), title, save_path)
        else:
            print("No data found in checkpoint file")
    
    elif args.checkpoint_dir:
        # Load from checkpoint directory
        print(f"Loading checkpoints from directory: {args.checkpoint_dir}")
        
        checkpoint_files = [f for f in os.listdir(args.checkpoint_dir) if f.endswith('.tar')]
        checkpoint_files.sort()
        
        if not checkpoint_files:
            print("No checkpoint files found")
            return
        
        if args.compare and len(checkpoint_files) > 1:
            # Compare multiple checkpoints
            models_data = []
            
            for checkpoint_file in checkpoint_files:
                checkpoint_path = os.path.join(args.checkpoint_dir, checkpoint_file)
                steps, losses, alpha, beta = extract_metrics_from_checkpoint(checkpoint_path)
                
                if steps:
                    model_name = f"{checkpoint_file} (α={alpha:.1f}, β={beta:.1f})" if alpha is not None else checkpoint_file
                    models_data.append((model_name, steps, losses, [0] * len(steps)))
            
            if models_data:
                save_path = os.path.join(args.output_dir, "checkpoints_comparison.png")
                plot_comparison_curves(models_data, save_path)
            else:
                print("No valid checkpoint data found")
        
        else:
            # Analyze latest checkpoint
            latest_checkpoint = os.path.join(args.checkpoint_dir, checkpoint_files[-1])
            steps, losses, alpha, beta = extract_metrics_from_checkpoint(latest_checkpoint)
            
            if steps:
                title = f"Latest Checkpoint (α={alpha:.1f}, β={beta:.1f})" if alpha is not None else "Latest Checkpoint"
                save_path = os.path.join(args.output_dir, "latest_checkpoint.png")
                plot_single_training_curve(steps, losses, [0] * len(steps), title, save_path)
            else:
                print("No data found in latest checkpoint")
    
    else:
        print("Please specify either --checkpoint-dir, --checkpoint-file, or --csv-file")
        print("Example usage:")
        print("  python3 plot_training_curves.py --csv-file ./cp_multi_obj/training_metrics_a0.5_b0.5.csv")
        print("  python3 plot_training_curves.py --checkpoint-dir ./cp_multi_obj")
        print("  python3 plot_training_curves.py --checkpoint-file ./cp_multi_obj/checkpoint_00500.tar")


if __name__ == "__main__":
    main()
