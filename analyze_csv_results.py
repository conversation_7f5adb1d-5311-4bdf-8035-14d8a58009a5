#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
analyze_csv_results.py

Load and analyze CSV results from test_models_csv_format.py output.
Generate comprehensive analysis and plots.
"""

import os
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import glob
from typing import Dict, List, <PERSON><PERSON>


def load_csv_results(results_dir: str) -> <PERSON><PERSON>[Dict[str, pd.DataFrame], Dict[str, pd.DataFrame]]:
    """
    Load makespan and workload balance CSV files.
    
    Returns:
        Tuple of (makespan_data, balance_data) dictionaries
    """
    makespan_files = glob.glob(os.path.join(results_dir, "makespans_*.csv"))
    balance_files = glob.glob(os.path.join(results_dir, "workload_balance_*.csv"))
    
    print(f"Found {len(makespan_files)} makespan files and {len(balance_files)} balance files")
    
    makespan_data = {}
    balance_data = {}
    
    # Load makespan files
    for file_path in makespan_files:
        filename = os.path.basename(file_path)
        model_name = filename.replace("makespans_", "").replace(".csv", "")
        try:
            df = pd.read_csv(file_path)
            makespan_data[model_name] = df
            print(f"  Loaded makespan data for {model_name}: {len(df)} instances")
        except Exception as e:
            print(f"  Error loading {filename}: {e}")
    
    # Load balance files
    for file_path in balance_files:
        filename = os.path.basename(file_path)
        model_name = filename.replace("workload_balance_", "").replace(".csv", "")
        try:
            df = pd.read_csv(file_path)
            balance_data[model_name] = df
            print(f"  Loaded balance data for {model_name}: {len(df)} instances")
        except Exception as e:
            print(f"  Error loading {filename}: {e}")
    
    return makespan_data, balance_data


def calculate_summary_statistics(makespan_data: Dict[str, pd.DataFrame], 
                                balance_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
    """Calculate summary statistics for all models."""
    summary_stats = []
    
    for model_name in makespan_data.keys():
        if model_name not in balance_data:
            continue
        
        makespan_df = makespan_data[model_name]
        balance_df = balance_data[model_name]
        
        # Get feasible instances
        feasible_makespan = makespan_df[makespan_df['feasible'] == 1]
        feasible_balance = balance_df[balance_df['feasible'] == 1]
        
        stats = {
            'model_name': model_name,
            'total_instances': len(makespan_df),
            'feasible_instances': len(feasible_makespan),
            'feasibility_rate': len(feasible_makespan) / len(makespan_df) if len(makespan_df) > 0 else 0,
            'avg_makespan': feasible_makespan['makespan'].mean() if len(feasible_makespan) > 0 else np.nan,
            'std_makespan': feasible_makespan['makespan'].std() if len(feasible_makespan) > 0 else np.nan,
            'min_makespan': feasible_makespan['makespan'].min() if len(feasible_makespan) > 0 else np.nan,
            'max_makespan': feasible_makespan['makespan'].max() if len(feasible_makespan) > 0 else np.nan,
            'avg_workload_balance': feasible_balance['workload_balance'].mean() if len(feasible_balance) > 0 else np.nan,
            'std_workload_balance': feasible_balance['workload_balance'].std() if len(feasible_balance) > 0 else np.nan,
            'min_workload_balance': feasible_balance['workload_balance'].min() if len(feasible_balance) > 0 else np.nan,
            'max_workload_balance': feasible_balance['workload_balance'].max() if len(feasible_balance) > 0 else np.nan,
            'avg_runtime': feasible_makespan['runtime'].mean() if len(feasible_makespan) > 0 else np.nan,
            'std_runtime': feasible_makespan['runtime'].std() if len(feasible_makespan) > 0 else np.nan
        }
        
        summary_stats.append(stats)
    
    return pd.DataFrame(summary_stats)


def create_comparison_plots(makespan_data: Dict[str, pd.DataFrame], 
                           balance_data: Dict[str, pd.DataFrame], 
                           summary_df: pd.DataFrame, 
                           output_dir: str):
    """Create comprehensive comparison plots."""
    
    # Set up plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. Performance Summary Plot
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Multi-Objective Model Performance Comparison', fontsize=16, fontweight='bold')
    
    # Feasibility rates
    ax1 = axes[0, 0]
    feasibility_data = summary_df.sort_values('feasibility_rate', ascending=True)
    bars1 = ax1.barh(feasibility_data['model_name'], feasibility_data['feasibility_rate'])
    ax1.set_xlabel('Feasibility Rate')
    ax1.set_title('Feasibility Rate by Model')
    ax1.set_xlim(0, 1)
    
    # Add value labels
    for i, bar in enumerate(bars1):
        width = bar.get_width()
        ax1.text(width + 0.01, bar.get_y() + bar.get_height()/2, 
                f'{width:.2f}', ha='left', va='center', fontsize=9)
    
    # Average makespan
    ax2 = axes[0, 1]
    makespan_data_plot = summary_df.dropna(subset=['avg_makespan']).sort_values('avg_makespan')
    bars2 = ax2.bar(range(len(makespan_data_plot)), makespan_data_plot['avg_makespan'], 
                   yerr=makespan_data_plot['std_makespan'], capsize=5)
    ax2.set_xlabel('Model')
    ax2.set_ylabel('Average Makespan')
    ax2.set_title('Average Makespan by Model')
    ax2.set_xticks(range(len(makespan_data_plot)))
    ax2.set_xticklabels(makespan_data_plot['model_name'], rotation=45, ha='right')
    
    # Average workload balance
    ax3 = axes[1, 0]
    balance_data_plot = summary_df.dropna(subset=['avg_workload_balance']).sort_values('avg_workload_balance')
    bars3 = ax3.bar(range(len(balance_data_plot)), balance_data_plot['avg_workload_balance'], 
                   yerr=balance_data_plot['std_workload_balance'], capsize=5)
    ax3.set_xlabel('Model')
    ax3.set_ylabel('Average Workload Balance')
    ax3.set_title('Average Workload Balance by Model (Lower is Better)')
    ax3.set_xticks(range(len(balance_data_plot)))
    ax3.set_xticklabels(balance_data_plot['model_name'], rotation=45, ha='right')
    
    # Runtime comparison
    ax4 = axes[1, 1]
    runtime_data_plot = summary_df.dropna(subset=['avg_runtime']).sort_values('avg_runtime')
    bars4 = ax4.bar(range(len(runtime_data_plot)), runtime_data_plot['avg_runtime'], 
                   yerr=runtime_data_plot['std_runtime'], capsize=5)
    ax4.set_xlabel('Model')
    ax4.set_ylabel('Average Runtime (seconds)')
    ax4.set_title('Average Runtime by Model')
    ax4.set_xticks(range(len(runtime_data_plot)))
    ax4.set_xticklabels(runtime_data_plot['model_name'], rotation=45, ha='right')
    
    plt.tight_layout()
    
    # Save plot
    plot_path = os.path.join(output_dir, 'performance_comparison.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Performance comparison plot saved to: {plot_path}")
    plt.show()


def create_pareto_front_analysis(makespan_data: Dict[str, pd.DataFrame], 
                                balance_data: Dict[str, pd.DataFrame], 
                                output_dir: str):
    """Create Pareto front analysis plot."""
    plt.figure(figsize=(12, 8))
    
    colors = plt.cm.Set1(np.linspace(0, 1, len(makespan_data)))
    markers = ['o', 's', '^', 'D', 'v', '<', '>', 'p', '*', 'h']
    
    all_points = []
    model_labels = []
    
    for i, model_name in enumerate(makespan_data.keys()):
        if model_name not in balance_data:
            continue
        
        makespan_df = makespan_data[model_name]
        balance_df = balance_data[model_name]
        
        # Get feasible instances
        feasible_makespan = makespan_df[makespan_df['feasible'] == 1]
        feasible_balance = balance_df[balance_df['feasible'] == 1]
        
        if len(feasible_makespan) == 0:
            continue
        
        # Merge data on instance_id
        merged_df = pd.merge(feasible_makespan[['instance_id', 'makespan']], 
                            feasible_balance[['instance_id', 'workload_balance']], 
                            on='instance_id')
        
        if len(merged_df) == 0:
            continue
        
        # Plot points
        plt.scatter(merged_df['makespan'], merged_df['workload_balance'], 
                   c=[colors[i]], marker=markers[i % len(markers)], 
                   s=60, alpha=0.7, label=model_name, edgecolors='black', linewidth=0.5)
        
        # Store points for Pareto analysis
        for _, row in merged_df.iterrows():
            all_points.append([row['makespan'], row['workload_balance']])
            model_labels.append(model_name)
    
    # Calculate Pareto front
    if all_points:
        all_points = np.array(all_points)
        pareto_indices = []
        
        for i, point in enumerate(all_points):
            is_pareto = True
            for j, other_point in enumerate(all_points):
                if i != j:
                    # A point is dominated if another point is better in both objectives
                    if (other_point[0] <= point[0] and other_point[1] <= point[1] and 
                        (other_point[0] < point[0] or other_point[1] < point[1])):
                        is_pareto = False
                        break
            if is_pareto:
                pareto_indices.append(i)
        
        if pareto_indices:
            pareto_points = all_points[pareto_indices]
            # Sort by makespan for plotting
            sorted_indices = np.argsort(pareto_points[:, 0])
            pareto_points = pareto_points[sorted_indices]
            
            plt.plot(pareto_points[:, 0], pareto_points[:, 1], 'r--', 
                    linewidth=2, alpha=0.8, label='Pareto Front')
            
            print(f"Pareto front contains {len(pareto_points)} solutions")
    
    plt.xlabel('Makespan (Lower is Better)')
    plt.ylabel('Workload Balance (Lower is Better)')
    plt.title('Pareto Front: Makespan vs Workload Balance')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    
    # Save plot
    plot_path = os.path.join(output_dir, 'pareto_front_analysis.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Pareto front analysis plot saved to: {plot_path}")
    plt.show()


def create_distribution_plots(makespan_data: Dict[str, pd.DataFrame], 
                             balance_data: Dict[str, pd.DataFrame], 
                             output_dir: str):
    """Create distribution analysis plots."""
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # Prepare data for box plots
    makespan_plot_data = []
    balance_plot_data = []
    model_names = []
    
    for model_name in makespan_data.keys():
        if model_name not in balance_data:
            continue
        
        makespan_df = makespan_data[model_name]
        balance_df = balance_data[model_name]
        
        feasible_makespan = makespan_df[makespan_df['feasible'] == 1]
        feasible_balance = balance_df[balance_df['feasible'] == 1]
        
        if len(feasible_makespan) > 0:
            makespan_plot_data.append(feasible_makespan['makespan'].values)
            balance_plot_data.append(feasible_balance['workload_balance'].values)
            model_names.append(model_name)
    
    # Makespan box plot
    ax1 = axes[0]
    if makespan_plot_data:
        bp1 = ax1.boxplot(makespan_plot_data, labels=model_names, patch_artist=True)
        ax1.set_title('Makespan Distribution by Model')
        ax1.set_ylabel('Makespan')
        ax1.tick_params(axis='x', rotation=45)
        
        # Color the boxes
        colors = plt.cm.Set3(np.linspace(0, 1, len(bp1['boxes'])))
        for patch, color in zip(bp1['boxes'], colors):
            patch.set_facecolor(color)
    
    # Workload balance box plot
    ax2 = axes[1]
    if balance_plot_data:
        bp2 = ax2.boxplot(balance_plot_data, labels=model_names, patch_artist=True)
        ax2.set_title('Workload Balance Distribution by Model')
        ax2.set_ylabel('Workload Balance')
        ax2.tick_params(axis='x', rotation=45)
        
        # Color the boxes
        colors = plt.cm.Set3(np.linspace(0, 1, len(bp2['boxes'])))
        for patch, color in zip(bp2['boxes'], colors):
            patch.set_facecolor(color)
    
    plt.tight_layout()
    
    # Save plot
    plot_path = os.path.join(output_dir, 'distribution_analysis.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"Distribution analysis plot saved to: {plot_path}")
    plt.show()


def generate_detailed_report(makespan_data: Dict[str, pd.DataFrame], 
                           balance_data: Dict[str, pd.DataFrame], 
                           summary_df: pd.DataFrame, 
                           output_dir: str):
    """Generate a detailed text report."""
    report_path = os.path.join(output_dir, 'analysis_report.txt')
    
    with open(report_path, 'w') as f:
        f.write("MULTI-OBJECTIVE MRC SCHEDULING ANALYSIS REPORT\n")
        f.write("=" * 60 + "\n\n")
        
        # Overall statistics
        f.write("OVERALL STATISTICS\n")
        f.write("-" * 30 + "\n")
        total_instances = summary_df['total_instances'].iloc[0] if len(summary_df) > 0 else 0
        f.write(f"Total instances per model: {total_instances}\n")
        f.write(f"Total models evaluated: {len(summary_df)}\n")
        f.write(f"Overall average feasibility rate: {summary_df['feasibility_rate'].mean():.3f}\n\n")
        
        # Model-by-model analysis
        f.write("MODEL PERFORMANCE SUMMARY\n")
        f.write("-" * 30 + "\n")
        
        for _, row in summary_df.iterrows():
            f.write(f"\nModel: {row['model_name']}\n")
            f.write(f"  Feasibility: {row['feasible_instances']}/{row['total_instances']} ({row['feasibility_rate']:.3f})\n")
            
            if row['feasible_instances'] > 0:
                f.write(f"  Makespan: {row['avg_makespan']:.2f} ± {row['std_makespan']:.2f} (range: {row['min_makespan']:.2f}-{row['max_makespan']:.2f})\n")
                f.write(f"  Workload Balance: {row['avg_workload_balance']:.3f} ± {row['std_workload_balance']:.3f} (range: {row['min_workload_balance']:.3f}-{row['max_workload_balance']:.3f})\n")
                f.write(f"  Runtime: {row['avg_runtime']:.3f} ± {row['std_runtime']:.3f} seconds\n")
            else:
                f.write("  No feasible solutions found\n")
        
        # Best performers
        f.write("\n\nBEST PERFORMERS\n")
        f.write("-" * 30 + "\n")
        
        feasible_models = summary_df[summary_df['feasible_instances'] > 0]
        
        if not feasible_models.empty:
            best_makespan = feasible_models.loc[feasible_models['avg_makespan'].idxmin()]
            best_balance = feasible_models.loc[feasible_models['avg_workload_balance'].idxmin()]
            best_feasibility = feasible_models.loc[feasible_models['feasibility_rate'].idxmax()]
            
            f.write(f"Best Makespan: {best_makespan['model_name']} ({best_makespan['avg_makespan']:.2f})\n")
            f.write(f"Best Workload Balance: {best_balance['model_name']} ({best_balance['avg_workload_balance']:.3f})\n")
            f.write(f"Best Feasibility: {best_feasibility['model_name']} ({best_feasibility['feasibility_rate']:.3f})\n")
    
    print(f"Detailed report saved to: {report_path}")


def main():
    parser = argparse.ArgumentParser(description="Analyze CSV results from multi-objective testing")
    parser.add_argument("--results-dir", default="./csv_outputs_multi_objective", 
                       help="Directory containing CSV result files")
    parser.add_argument("--output-dir", help="Output directory for analysis (default: same as results-dir)")
    
    args = parser.parse_args()
    
    if args.output_dir is None:
        args.output_dir = args.results_dir
    
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("Multi-Objective CSV Results Analysis")
    print("=" * 50)
    print(f"Results directory: {args.results_dir}")
    print(f"Output directory: {args.output_dir}")
    
    # Load CSV results
    makespan_data, balance_data = load_csv_results(args.results_dir)
    
    if not makespan_data or not balance_data:
        print("No CSV data found. Please run test_models_csv_format.py first.")
        return
    
    # Calculate summary statistics
    summary_df = calculate_summary_statistics(makespan_data, balance_data)
    
    # Save summary statistics
    summary_path = os.path.join(args.output_dir, 'summary_statistics.csv')
    summary_df.to_csv(summary_path, index=False)
    print(f"\nSummary statistics saved to: {summary_path}")
    
    # Display summary
    print("\nSUMMARY STATISTICS:")
    display_cols = ['model_name', 'feasibility_rate', 'avg_makespan', 'avg_workload_balance', 'avg_runtime']
    print(summary_df[display_cols].to_string(index=False, float_format='%.3f'))
    
    # Generate plots and analysis
    print("\nGenerating analysis plots...")
    create_comparison_plots(makespan_data, balance_data, summary_df, args.output_dir)
    create_pareto_front_analysis(makespan_data, balance_data, args.output_dir)
    create_distribution_plots(makespan_data, balance_data, args.output_dir)
    
    # Generate detailed report
    generate_detailed_report(makespan_data, balance_data, summary_df, args.output_dir)
    
    print("\n=== ANALYSIS COMPLETED ===")
    print(f"All results saved to: {args.output_dir}")


if __name__ == "__main__":
    main()
