#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
multi_objective_train.py

Multi-objective training script for SSAN with makespan and workload balance objectives.
Based on lr_scheduler_train.py but modified for multi-objective optimization.
"""

import os
import sys
import argparse
import time
import copy
import numpy as np
import torch
import torch.nn.functional as F
import torch.nn.utils as utils
from torch.optim.lr_scheduler import ReduceLROnPlateau

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper, Transition
from hetnet import ScheduleNet4Layer
from multi_objective_utils import calculate_workload_balance_from_env


class MultiObjectiveRewardStabilizer:
    """Stabilizes multi-objective rewards for better training."""
    
    def __init__(self, window_size=50, alpha_ema=0.1):
        self.window_size = window_size
        self.alpha_ema = alpha_ema
        self.makespan_history = []
        self.balance_history = []
        self.combined_history = []
        self.makespan_ema = None
        self.balance_ema = None
        self.combined_ema = None
    
    def update(self, makespan_reward, balance_reward, combined_reward):
        """Update reward histories and EMAs."""
        self.makespan_history.append(makespan_reward)
        self.balance_history.append(balance_reward)
        self.combined_history.append(combined_reward)
        
        # Keep only recent history
        if len(self.makespan_history) > self.window_size:
            self.makespan_history.pop(0)
            self.balance_history.pop(0)
            self.combined_history.pop(0)
        
        # Update EMAs
        if self.makespan_ema is None:
            self.makespan_ema = makespan_reward
            self.balance_ema = balance_reward
            self.combined_ema = combined_reward
        else:
            self.makespan_ema = (1 - self.alpha_ema) * self.makespan_ema + self.alpha_ema * makespan_reward
            self.balance_ema = (1 - self.alpha_ema) * self.balance_ema + self.alpha_ema * balance_reward
            self.combined_ema = (1 - self.alpha_ema) * self.combined_ema + self.alpha_ema * combined_reward
    
    def get_stabilized_reward(self, makespan_reward, balance_reward, combined_reward):
        """Get stabilized version of the combined reward."""
        self.update(makespan_reward, balance_reward, combined_reward)
        
        if len(self.combined_history) < 5:
            return combined_reward
        
        # Use EMA for stabilization
        return self.combined_ema


def collect_multi_objective_data(data_dir, start_no, end_no, num_robots, alpha=0.5, beta=0.5):
    """
    Collect training data with multi-objective rewards.
    
    Args:
        data_dir: Directory containing problem instances
        start_no: Starting instance number
        end_no: Ending instance number
        num_robots: Number of robots
        alpha: Weight for makespan objective
        beta: Weight for workload balance objective
    
    Returns:
        List of transitions with multi-objective rewards
    """
    memory = []
    
    for inst_no in range(start_no, end_no + 1):
        fname = os.path.join(data_dir, f"{inst_no:05d}")
        
        # Check if files exist
        if not os.path.isfile(f"{fname}_dur.txt"):
            print(f"Skipping instance {inst_no:05d} - files not found")
            continue
        
        try:
            # Load environment and set multi-objective parameters
            env = SchedulingEnv(fname)
            env.set_multi_objective_params(alpha=alpha, beta=beta)
            
            # Load optimal solution from solutions directory
            optimals = []
            solutions_dir = data_dir.replace("/constraints", "/solutions")
            solution_prefix = os.path.join(solutions_dir, f"{inst_no:05d}")

            for i in range(num_robots):
                opt_file = f"{solution_prefix}_{i}.txt"
                if os.path.isfile(opt_file):
                    optimals.append(np.loadtxt(opt_file, dtype=np.int32))
                else:
                    print(f"Warning: Optimal solution file {opt_file} not found")
                    continue

            optimalw_file = f"{solution_prefix}_w.txt"
            if not os.path.isfile(optimalw_file):
                print(f"Warning: Optimal sequence file {optimalw_file} not found")
                continue
            
            optimalw = np.loadtxt(optimalw_file, dtype=np.int32)
            
            # Collect trajectory data
            state_graphs = []
            partials = []
            partialw = []
            actions_task = []
            actions_robot = []
            rewards = []
            makespan_rewards = []
            balance_rewards = []
            terminates = []
            
            # Initial state
            state_graphs.append(copy.deepcopy(env.halfDG))
            partials.append(copy.deepcopy(env.partials))
            partialw.append(copy.deepcopy(env.partialw))
            terminates.append(False)
            
            # Execute optimal trajectory
            for i in range(env.num_tasks):
                # Find which robot should execute this task
                task = optimalw[i]
                robot = None
                for r in range(num_robots):
                    if len(optimals) > r and task in optimals[r]:
                        robot = r
                        break
                
                if robot is None:
                    print(f"Warning: Task {task} not found in any robot's optimal solution")
                    robot = 0  # Default to robot 0
                
                # Insert task into environment and get multi-objective reward
                success, reward, done_flag = env.insert_robot(task, robot)

                if not success:
                    print(f"Warning: Infeasible action at step {i} for instance {inst_no}")
                    break

                # Store transition data
                state_graphs.append(copy.deepcopy(env.halfDG))
                partials.append(copy.deepcopy(env.partials))
                partialw.append(copy.deepcopy(env.partialw))
                actions_task.append(task)
                actions_robot.append(robot)
                rewards.append(reward)
                makespan_rewards.append(reward)  # For now, use the combined reward
                balance_rewards.append(0.0)     # Placeholder
                terminates.append(i == env.num_tasks - 1)
            
            # Create transitions
            for i in range(len(actions_task)):
                transition = Transition(
                    curr_g=state_graphs[i],
                    curr_partials=partials[i],
                    curr_partialw=partialw[i],
                    locs=env.loc,
                    durs=env.dur,
                    act_task=actions_task[i],
                    act_robot=actions_robot[i],
                    reward_n=rewards[i],
                    next_g=state_graphs[i + 1],
                    next_partials=partials[i + 1],
                    next_partialw=partialw[i + 1],
                    next_done=terminates[i]
                )
                memory.append(transition)
            
            print(f"Collected {len(actions_task)} transitions from instance {inst_no:05d}")
            
        except Exception as e:
            print(f"Error processing instance {inst_no:05d}: {e}")
            continue
    
    print(f"Total transitions collected: {len(memory)}")
    return memory


def main():
    parser = argparse.ArgumentParser(description="Multi-objective SSAN training")
    parser.add_argument("--path-to-train", default= "./problem_instances/constraints" \
    "", type=str)
    parser.add_argument("--num-robots", type=int, default=2, help="Number of robots")
    parser.add_argument("--train-start-no", type=int, default=1, help="Start instance number")
    parser.add_argument("--train-end-no", type=int, default=100, help="End instance number")
    parser.add_argument("--steps", type=int, default=500, help="Training steps")
    parser.add_argument("--batch-size", type=int, default=8, help="Batch size")
    parser.add_argument("--lr", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--weight-decay", type=float, default=1e-6, help="Weight decay")
    parser.add_argument("--alpha", type=float, default=0.5, help="Weight for makespan objective")
    parser.add_argument("--beta", type=float, default=0.5, help="Weight for workload balance objective")
    parser.add_argument("--checkpoint-interval", type=int, default=50, help="Checkpoint save interval")
    parser.add_argument("--cpsave", default="./cp_multi_obj", help="Checkpoint save directory")
    parser.add_argument("--device", default="cpu")
    
    args = parser.parse_args()
    
    # Validate weights
    if abs(args.alpha + args.beta - 1.0) > 1e-6:
        print(f"Warning: alpha + beta = {args.alpha + args.beta} != 1.0")
    
    # Setup
    device = torch.device(args.device)
    os.makedirs(args.cpsave, exist_ok=True)
    
    print(f"Multi-objective training with α={args.alpha}, β={args.beta}")
    print(f"Training on instances {args.train_start_no} to {args.train_end_no}")
    
    # Collect training data
    print("Collecting multi-objective training data...")
    memory = collect_multi_objective_data(
        args.path_to_train, 
        args.train_start_no, 
        args.train_end_no,
        args.num_robots,
        args.alpha,
        args.beta
    )
    
    if len(memory) == 0:
        print("No training data collected. Exiting.")
        return
    
    # Network setup
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
    optimizer = torch.optim.Adam(policy_net.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    lr_scheduler = ReduceLROnPlateau(optimizer, 'min', factor=0.5, patience=20, min_lr=1e-7)
    
    # Multi-objective reward stabilizer
    reward_stabilizer = MultiObjectiveRewardStabilizer()
    
    print(f"Starting multi-objective training for {args.steps} steps...")
    
    # Training loop
    for step in range(1, args.steps + 1):
        start_time = time.time()
        policy_net.train()
        
        # Sample batch
        batch_indices = np.random.choice(len(memory), min(args.batch_size, len(memory)), replace=False)
        batch = [memory[i] for i in batch_indices]
        
        total_loss = 0.0
        batch_rewards = []
        
        for transition in batch:
            # Build graph and features
            unsch_tasks = []
            for task_id in range(1, transition.durs.shape[0] + 1):
                if task_id not in transition.curr_partialw:
                    unsch_tasks.append(task_id)
            
            if len(unsch_tasks) == 0:
                continue
            
            # Build heterogeneous graph
            g = build_hetgraph(
                transition.curr_g,
                transition.durs.shape[0],
                args.num_robots,
                transition.durs.astype(np.float32),
                6,  # map_width
                np.array(transition.locs, dtype=np.int64),
                1,  # loc_dist_threshold
                transition.curr_partials,
                np.array(unsch_tasks, dtype=np.int32),
                transition.act_robot,
                np.array(unsch_tasks, dtype=np.int32)
            ).to(device)
            
            # Build features
            feat_dict = hetgraph_node_helper(
                transition.curr_g.number_of_nodes(),
                transition.curr_partialw,
                transition.curr_partials,
                transition.locs,
                transition.durs,
                6,  # map_width
                args.num_robots,
                len(unsch_tasks)
            )
            
            feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) for k, v in feat_dict.items()}
            
            # Forward pass
            outputs = policy_net(g, feat_tensors)
            q_values = outputs['value']
            
            # Prepare targets
            num_actions = len(unsch_tasks)
            if num_actions > 1:
                # Multi-action case
                offset = 0.01
                targets = torch.full((num_actions, 1), float(transition.reward_n) - offset,
                                   device=device, dtype=torch.float32)
                weights = torch.full((num_actions, 1), 0.8 / (num_actions - 1),
                                   device=device, dtype=torch.float32)

                # Find expert action
                expert_idx = 0
                for j, task in enumerate(unsch_tasks):
                    if task == transition.act_task:
                        expert_idx = j
                        break

                targets[expert_idx, 0] = float(transition.reward_n)
                weights[expert_idx, 0] = 1.0
            else:
                # Single action case
                targets = torch.tensor([[float(transition.reward_n)]], device=device, dtype=torch.float32)
                weights = torch.tensor([[1.0]], device=device, dtype=torch.float32)
            
            # Compute loss
            loss = F.mse_loss(q_values, targets, reduction='none')
            loss = (loss * weights).sum() / args.batch_size
            total_loss += loss
            
            batch_rewards.append(float(transition.reward_n))
        
        # Backward pass
        optimizer.zero_grad()
        total_loss.backward()
        utils.clip_grad_norm_(policy_net.parameters(), max_norm=1.0)
        optimizer.step()
        
        # Update learning rate
        lr_scheduler.step(total_loss.item())
        
        # Logging
        end_time = time.time()
        avg_reward = np.mean(batch_rewards) if batch_rewards else 0.0
        current_lr = optimizer.param_groups[0]['lr']
        
        print(f"[Step {step:4d}] Loss: {total_loss.item():.6f}, "
              f"Avg Reward: {avg_reward:.4f}, LR: {current_lr:.2e}, "
              f"Time: {end_time - start_time:.2f}s")
        
        # Save checkpoint
        if step % args.checkpoint_interval == 0:
            checkpoint_path = os.path.join(args.cpsave, f"checkpoint_{step:05d}.tar")
            torch.save({
                'step': step,
                'policy_net_state_dict': policy_net.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'lr_scheduler_state_dict': lr_scheduler.state_dict(),
                'alpha': args.alpha,
                'beta': args.beta,
                'loss': total_loss.item()
            }, checkpoint_path)
            print(f"Checkpoint saved: {checkpoint_path}")
    
    print("Multi-objective training completed!")


if __name__ == "__main__":
    main()
