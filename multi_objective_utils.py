#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
multi_objective_utils.py

Multi-objective optimization utilities for MRC scheduling.
Supports makespan minimization and workload balance optimization.

Key Features:
- Workload balance calculation (minimizes variance in task assignments)
- Weighted sum multi-objective optimization
- Pareto front analysis
- Multi-objective evaluation metrics
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union
import copy


def calculate_workload_balance(assignments: Dict[int, List[int]], num_robots: int) -> float:
    """
    Calculate workload balance metric.
    
    Args:
        assignments: Dict mapping robot_id -> list of assigned task_ids
        num_robots: Total number of robots
    
    Returns:
        Workload balance score (lower is better)
        - 0.0 = perfectly balanced
        - Higher values = more imbalanced
    """
    # Count tasks per robot
    task_counts = np.zeros(num_robots)
    for robot_id, tasks in assignments.items():
        if robot_id < num_robots:
            task_counts[robot_id] = len(tasks)
    
    # Calculate variance in task counts
    mean_tasks = np.mean(task_counts)
    variance = np.var(task_counts)
    
    # Return standard deviation as balance metric
    return np.sqrt(variance)


def calculate_workload_balance_from_env(env) -> float:
    """
    Calculate workload balance from SchedulingEnv state.
    
    Args:
        env: SchedulingEnv instance with current partial schedules
    
    Returns:
        Workload balance score (lower is better)
    """
    assignments = {}
    for robot_id in range(env.num_robots):
        # Extract tasks from partial schedule (excluding task 0 which is start marker)
        tasks = [t for t in env.partials[robot_id] if t != 0]
        assignments[robot_id] = tasks
    
    return calculate_workload_balance(assignments, env.num_robots)


def calculate_weighted_objective(makespan: float, workload_balance: float, 
                               alpha: float = 0.5, beta: float = 0.5,
                               makespan_weight: float = 1.0, 
                               balance_weight: float = 1.0) -> float:
    """
    Calculate weighted sum of makespan and workload balance objectives.
    
    Args:
        makespan: Makespan value
        workload_balance: Workload balance value
        alpha: Weight for makespan objective (0-1)
        beta: Weight for workload balance objective (0-1)
        makespan_weight: Normalization weight for makespan
        balance_weight: Normalization weight for workload balance
    
    Returns:
        Weighted objective value
    """
    # Normalize objectives
    normalized_makespan = makespan / makespan_weight
    normalized_balance = workload_balance / balance_weight
    
    # Weighted sum
    return alpha * normalized_makespan + beta * normalized_balance


class MultiObjectiveMetrics:
    """Class for tracking and analyzing multi-objective optimization results."""
    
    def __init__(self):
        self.solutions = []  # List of (makespan, workload_balance, metadata) tuples
        
    def add_solution(self, makespan: float, workload_balance: float, 
                    metadata: Optional[Dict] = None):
        """Add a solution to the metrics tracker."""
        if metadata is None:
            metadata = {}
        self.solutions.append((makespan, workload_balance, metadata))
    
    def get_pareto_front(self) -> List[Tuple[float, float, Dict]]:
        """
        Calculate Pareto front from all solutions.
        
        Returns:
            List of non-dominated solutions (makespan, workload_balance, metadata)
        """
        if not self.solutions:
            return []
        
        pareto_front = []
        
        for i, (mk1, wb1, meta1) in enumerate(self.solutions):
            is_dominated = False
            
            for j, (mk2, wb2, meta2) in enumerate(self.solutions):
                if i != j:
                    # Check if solution i is dominated by solution j
                    # (j is better in both objectives or equal in one and better in other)
                    if (mk2 <= mk1 and wb2 <= wb1) and (mk2 < mk1 or wb2 < wb1):
                        is_dominated = True
                        break
            
            if not is_dominated:
                pareto_front.append((mk1, wb1, meta1))
        
        return pareto_front
    
    def plot_pareto_front(self, save_path: Optional[str] = None, 
                         title: str = "Pareto Front: Makespan vs Workload Balance"):
        """
        Plot Pareto front and all solutions.
        
        Args:
            save_path: Path to save the plot (optional)
            title: Plot title
        """
        if not self.solutions:
            print("No solutions to plot")
            return
        
        # Extract all solutions
        makespans = [sol[0] for sol in self.solutions]
        balances = [sol[1] for sol in self.solutions]
        
        # Get Pareto front
        pareto_front = self.get_pareto_front()
        pareto_makespans = [sol[0] for sol in pareto_front]
        pareto_balances = [sol[1] for sol in pareto_front]
        
        # Create plot
        plt.figure(figsize=(10, 8))
        
        # Plot all solutions
        plt.scatter(makespans, balances, alpha=0.6, c='lightblue', 
                   label=f'All Solutions ({len(self.solutions)})', s=50)
        
        # Plot Pareto front
        if pareto_front:
            plt.scatter(pareto_makespans, pareto_balances, c='red', 
                       label=f'Pareto Front ({len(pareto_front)})', s=100, marker='*')
            
            # Connect Pareto front points
            if len(pareto_front) > 1:
                # Sort by makespan for connecting line
                sorted_pareto = sorted(pareto_front, key=lambda x: x[0])
                sorted_makespans = [sol[0] for sol in sorted_pareto]
                sorted_balances = [sol[1] for sol in sorted_pareto]
                plt.plot(sorted_makespans, sorted_balances, 'r--', alpha=0.7)
        
        plt.xlabel('Makespan')
        plt.ylabel('Workload Balance (Standard Deviation)')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Pareto front plot saved to: {save_path}")
        
        plt.show()
    
    def get_statistics(self) -> Dict:
        """Get summary statistics for all solutions."""
        if not self.solutions:
            return {}
        
        makespans = [sol[0] for sol in self.solutions]
        balances = [sol[1] for sol in self.solutions]
        
        pareto_front = self.get_pareto_front()
        
        return {
            'total_solutions': len(self.solutions),
            'pareto_front_size': len(pareto_front),
            'makespan_stats': {
                'min': np.min(makespans),
                'max': np.max(makespans),
                'mean': np.mean(makespans),
                'std': np.std(makespans)
            },
            'balance_stats': {
                'min': np.min(balances),
                'max': np.max(balances),
                'mean': np.mean(balances),
                'std': np.std(balances)
            }
        }


def normalize_objectives(makespans: List[float], balances: List[float]) -> Tuple[List[float], List[float]]:
    """
    Normalize objectives to [0, 1] range for fair comparison.
    
    Args:
        makespans: List of makespan values
        balances: List of workload balance values
    
    Returns:
        Tuple of (normalized_makespans, normalized_balances)
    """
    if not makespans or not balances:
        return [], []
    
    # Min-max normalization
    mk_min, mk_max = np.min(makespans), np.max(makespans)
    bal_min, bal_max = np.min(balances), np.max(balances)
    
    # Avoid division by zero
    mk_range = mk_max - mk_min if mk_max > mk_min else 1.0
    bal_range = bal_max - bal_min if bal_max > bal_min else 1.0
    
    norm_makespans = [(mk - mk_min) / mk_range for mk in makespans]
    norm_balances = [(bal - bal_min) / bal_range for bal in balances]
    
    return norm_makespans, norm_balances
