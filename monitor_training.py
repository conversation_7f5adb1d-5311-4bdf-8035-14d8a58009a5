#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
monitor_training.py

Real-time training monitor that can parse training logs and update plots.
Useful for monitoring long training runs.
"""

import os
import time
import re
import argparse
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from collections import deque
import numpy as np


class TrainingMonitor:
    """Real-time training monitor."""
    
    def __init__(self, log_file=None, max_points=1000):
        self.log_file = log_file
        self.max_points = max_points
        
        # Data storage
        self.steps = deque(maxlen=max_points)
        self.losses = deque(maxlen=max_points)
        self.rewards = deque(maxlen=max_points)
        self.learning_rates = deque(maxlen=max_points)
        
        # Regex pattern to parse training logs
        self.log_pattern = re.compile(
            r'\[Step\s+(\d+)\]\s+Loss:\s+([\d.]+),\s+Avg Reward:\s+([-\d.]+),\s+LR:\s+([\d.e-]+)'
        )
        
        # File position for reading new lines
        self.file_position = 0
        
        # Setup plot
        self.fig, self.axes = plt.subplots(2, 2, figsize=(15, 10))
        self.fig.suptitle('Multi-Objective Training Monitor', fontsize=16)
        
        # Initialize plots
        self.loss_line, = self.axes[0, 0].plot([], [], 'b-', linewidth=2)
        self.axes[0, 0].set_title('Training Loss')
        self.axes[0, 0].set_xlabel('Step')
        self.axes[0, 0].set_ylabel('Loss')
        self.axes[0, 0].grid(True, alpha=0.3)
        
        self.reward_line, = self.axes[0, 1].plot([], [], 'g-', linewidth=2)
        self.axes[0, 1].set_title('Average Reward')
        self.axes[0, 1].set_xlabel('Step')
        self.axes[0, 1].set_ylabel('Reward')
        self.axes[0, 1].grid(True, alpha=0.3)
        
        self.lr_line, = self.axes[1, 0].plot([], [], 'r-', linewidth=2)
        self.axes[1, 0].set_title('Learning Rate')
        self.axes[1, 0].set_xlabel('Step')
        self.axes[1, 0].set_ylabel('Learning Rate')
        self.axes[1, 0].set_yscale('log')
        self.axes[1, 0].grid(True, alpha=0.3)
        
        # Statistics text
        self.stats_text = self.axes[1, 1].text(0.1, 0.9, '', transform=self.axes[1, 1].transAxes,
                                              fontsize=10, verticalalignment='top', fontfamily='monospace')
        self.axes[1, 1].set_title('Training Statistics')
        self.axes[1, 1].axis('off')
        
        plt.tight_layout()
    
    def parse_log_line(self, line):
        """Parse a single log line and extract training metrics."""
        match = self.log_pattern.search(line)
        if match:
            step = int(match.group(1))
            loss = float(match.group(2))
            reward = float(match.group(3))
            lr = float(match.group(4))
            return step, loss, reward, lr
        return None
    
    def read_new_lines(self):
        """Read new lines from the log file."""
        if not self.log_file or not os.path.exists(self.log_file):
            return []
        
        new_lines = []
        try:
            with open(self.log_file, 'r') as f:
                f.seek(self.file_position)
                new_lines = f.readlines()
                self.file_position = f.tell()
        except Exception as e:
            print(f"Error reading log file: {e}")
        
        return new_lines
    
    def update_data(self):
        """Update data from log file."""
        new_lines = self.read_new_lines()
        
        for line in new_lines:
            parsed = self.parse_log_line(line)
            if parsed:
                step, loss, reward, lr = parsed
                self.steps.append(step)
                self.losses.append(loss)
                self.rewards.append(reward)
                self.learning_rates.append(lr)
    
    def update_plots(self, frame):
        """Update plots with new data."""
        self.update_data()
        
        if len(self.steps) == 0:
            return self.loss_line, self.reward_line, self.lr_line
        
        steps_list = list(self.steps)
        losses_list = list(self.losses)
        rewards_list = list(self.rewards)
        lr_list = list(self.learning_rates)
        
        # Update loss plot
        self.loss_line.set_data(steps_list, losses_list)
        self.axes[0, 0].relim()
        self.axes[0, 0].autoscale_view()
        
        # Update reward plot
        self.reward_line.set_data(steps_list, rewards_list)
        self.axes[0, 1].relim()
        self.axes[0, 1].autoscale_view()
        
        # Update learning rate plot
        self.lr_line.set_data(steps_list, lr_list)
        self.axes[1, 0].relim()
        self.axes[1, 0].autoscale_view()
        
        # Update statistics
        if len(steps_list) > 0:
            stats_text = self.generate_stats_text(steps_list, losses_list, rewards_list, lr_list)
            self.stats_text.set_text(stats_text)
        
        return self.loss_line, self.reward_line, self.lr_line
    
    def generate_stats_text(self, steps, losses, rewards, lrs):
        """Generate statistics text."""
        if not steps:
            return "No data available"
        
        current_step = steps[-1]
        current_loss = losses[-1]
        current_reward = rewards[-1]
        current_lr = lrs[-1]
        
        # Calculate trends (last 20% of data)
        trend_window = max(1, len(steps) // 5)
        recent_losses = losses[-trend_window:]
        recent_rewards = rewards[-trend_window:]
        
        loss_trend = "↓" if len(recent_losses) > 1 and recent_losses[-1] < recent_losses[0] else "↑"
        reward_trend = "↑" if len(recent_rewards) > 1 and recent_rewards[-1] > recent_rewards[0] else "↓"
        
        # Best values
        best_loss = min(losses)
        best_reward = max(rewards)
        
        stats = f"""Current Status:
Step: {current_step}
Loss: {current_loss:.6f} {loss_trend}
Reward: {current_reward:.4f} {reward_trend}
Learning Rate: {current_lr:.2e}

Best Values:
Best Loss: {best_loss:.6f}
Best Reward: {best_reward:.4f}

Progress:
Total Steps: {len(steps)}
Loss Reduction: {((losses[0] - current_loss) / losses[0] * 100):.1f}%
Reward Change: {(current_reward - rewards[0]):.4f}

Trends (recent):
Loss Std: {np.std(recent_losses):.6f}
Reward Std: {np.std(recent_rewards):.6f}"""
        
        return stats
    
    def start_monitoring(self, interval=2000):
        """Start real-time monitoring."""
        print(f"Starting training monitor...")
        if self.log_file:
            print(f"Monitoring log file: {self.log_file}")
        print("Close the plot window to stop monitoring.")
        
        # Start animation
        ani = animation.FuncAnimation(self.fig, self.update_plots, interval=interval, blit=False)
        plt.show()
        
        return ani


def monitor_from_terminal_output():
    """Monitor training from terminal output (manual input)."""
    monitor = TrainingMonitor()
    
    print("Manual Training Monitor")
    print("Enter training log lines (or 'quit' to exit):")
    print("Expected format: [Step    1] Loss: 2.345678, Avg Reward: -1.2345, LR: 1.00e-04, Time: 3.45s")
    
    while True:
        try:
            line = input("> ")
            if line.lower() in ['quit', 'exit', 'q']:
                break
            
            parsed = monitor.parse_log_line(line)
            if parsed:
                step, loss, reward, lr = parsed
                monitor.steps.append(step)
                monitor.losses.append(loss)
                monitor.rewards.append(reward)
                monitor.learning_rates.append(lr)
                
                print(f"Added: Step {step}, Loss {loss:.6f}, Reward {reward:.4f}")
                
                # Update plots
                monitor.update_plots(None)
                plt.pause(0.1)
            else:
                print("Could not parse line. Please check format.")
                
        except KeyboardInterrupt:
            break
        except EOFError:
            break
    
    print("Monitoring stopped.")


def main():
    parser = argparse.ArgumentParser(description="Monitor multi-objective training in real-time")
    parser.add_argument("--log-file", help="Path to training log file")
    parser.add_argument("--interval", type=int, default=2000, help="Update interval in milliseconds")
    parser.add_argument("--manual", action="store_true", help="Manual input mode")
    
    args = parser.parse_args()
    
    if args.manual:
        monitor_from_terminal_output()
    else:
        monitor = TrainingMonitor(args.log_file)
        monitor.start_monitoring(args.interval)


if __name__ == "__main__":
    main()
