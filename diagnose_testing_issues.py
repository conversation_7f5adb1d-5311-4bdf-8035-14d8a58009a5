#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
diagnose_testing_issues.py

Diagnostic script to identify and fix testing issues step by step.
"""

import os
import sys
import traceback
import numpy as np
import torch


def test_basic_imports():
    """Test if all required modules can be imported."""
    print("=== Testing Basic Imports ===")
    
    try:
        from utils import SchedulingEnv
        print("✓ SchedulingEnv imported successfully")
    except Exception as e:
        print(f"✗ Failed to import SchedulingEnv: {e}")
        return False
    
    try:
        from hetnet import ScheduleNet4Layer
        print("✓ ScheduleNet4Layer imported successfully")
    except Exception as e:
        print(f"✗ Failed to import ScheduleNet4Layer: {e}")
        return False
    
    try:
        from multi_objective_utils import calculate_workload_balance
        print("✓ Multi-objective utils imported successfully")
    except Exception as e:
        print(f"✗ Failed to import multi-objective utils: {e}")
        return False
    
    return True


def test_environment_loading():
    """Test loading a simple environment."""
    print("\n=== Testing Environment Loading ===")
    
    # Find a test instance
    test_paths = [
        "./problem_instances_test/constraints/00001",
        "./problem_instances/constraints/00001",
        "./data/00374"
    ]
    
    test_instance = None
    for path in test_paths:
        if os.path.isfile(f"{path}_dur.txt"):
            test_instance = path
            break
    
    if not test_instance:
        print("✗ No test instance found")
        return False
    
    print(f"Using test instance: {test_instance}")
    
    try:
        from utils import SchedulingEnv
        
        # Test standard environment
        env = SchedulingEnv(test_instance)
        print(f"✓ Standard environment loaded: {env.num_tasks} tasks, {env.num_robots} robots")
        
        # Test multi-objective environment
        env.set_multi_objective_params(alpha=0.5, beta=0.5)
        print(f"✓ Multi-objective parameters set: α={env.alpha}, β={env.beta}")
        
        # Test basic operations
        unsch_tasks = env.get_unscheduled_tasks()
        print(f"✓ Unscheduled tasks: {len(unsch_tasks)}")
        
        # Test workload balance calculation
        balance = env.calculate_workload_balance()
        print(f"✓ Initial workload balance: {balance:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Environment loading failed: {e}")
        traceback.print_exc()
        return False


def test_model_loading():
    """Test loading the trained model."""
    print("\n=== Testing Model Loading ===")
    
    model_paths = [
        "./cp_multi_obj/checkpoint_00500.tar",
        "./cp/checkpoint_00500.tar",
        "./checkpoints/checkpoint_00100.tar"
    ]
    
    model_path = None
    for path in model_paths:
        if os.path.isfile(path):
            model_path = path
            break
    
    if not model_path:
        print("✗ No model checkpoint found")
        return False
    
    print(f"Using model: {model_path}")
    
    try:
        from hetnet import ScheduleNet4Layer
        
        # Network architecture
        in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
        hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
        out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
        cetypes = [
            ('task', 'temporal', 'task'),
            ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
            ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
            ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
            ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
            ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
            ('state', 'sto', 'value'), ('value', 'vto', 'value'),
            ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
        ]
        
        device = torch.device('cpu')
        policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
        print("✓ Network architecture created")
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location=device)
        policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
        policy_net.eval()
        print("✓ Model weights loaded")
        
        alpha = checkpoint.get('alpha', 0.5)
        beta = checkpoint.get('beta', 0.5)
        print(f"✓ Training parameters: α={alpha:.3f}, β={beta:.3f}")
        
        return True, policy_net, alpha, beta
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        traceback.print_exc()
        return False, None, None, None


def test_graph_building():
    """Test heterogeneous graph building."""
    print("\n=== Testing Graph Building ===")
    
    # Find test instance
    test_paths = [
        "./problem_instances_test/constraints/00001",
        "./problem_instances/constraints/00001",
        "./data/00374"
    ]
    
    test_instance = None
    for path in test_paths:
        if os.path.isfile(f"{path}_dur.txt"):
            test_instance = path
            break
    
    if not test_instance:
        print("✗ No test instance found")
        return False
    
    try:
        from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
        
        env = SchedulingEnv(test_instance)
        env.set_multi_objective_params(alpha=0.5, beta=0.5)
        
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            print("✗ No unscheduled tasks available")
            return False
        
        print(f"Building graph for {len(unsch_tasks)} unscheduled tasks")
        
        # Build graph with correct data types
        g = build_hetgraph(
            env.halfDG,
            env.num_tasks,
            env.num_robots,
            env.dur.astype(np.float32),  # Ensure float32
            6,  # map_width
            np.array(env.loc, dtype=np.int64),
            1,  # loc_dist_threshold
            env.partials,
            np.array(unsch_tasks, dtype=np.int32),  # Ensure int32
            0,  # robot
            np.array(unsch_tasks, dtype=np.int32)   # Ensure int32
        )
        
        print(f"✓ Graph built: {g.number_of_nodes()} nodes")
        
        # Build features
        feat_dict = hetgraph_node_helper(
            env.halfDG.number_of_nodes(),
            env.partialw,
            env.partials,
            env.loc,
            env.dur,
            6,  # map_width
            env.num_robots,
            len(unsch_tasks)
        )
        
        print(f"✓ Features built: {list(feat_dict.keys())}")
        
        # Test tensor conversion
        device = torch.device('cpu')
        feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) 
                       for k, v in feat_dict.items()}
        
        print("✓ Feature tensors created with correct data types")
        
        return True
        
    except Exception as e:
        print(f"✗ Graph building failed: {e}")
        traceback.print_exc()
        return False


def test_simple_inference():
    """Test a simple inference step."""
    print("\n=== Testing Simple Inference ===")
    
    # Load model
    success, policy_net, alpha, beta = test_model_loading()
    if not success:
        return False
    
    # Find test instance
    test_paths = [
        "./problem_instances_test/constraints/00001",
        "./problem_instances/constraints/00001",
        "./data/00374"
    ]
    
    test_instance = None
    for path in test_paths:
        if os.path.isfile(f"{path}_dur.txt"):
            test_instance = path
            break
    
    if not test_instance:
        print("✗ No test instance found")
        return False
    
    try:
        from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
        
        env = SchedulingEnv(test_instance)
        env.set_multi_objective_params(alpha=alpha, beta=beta)
        
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            print("✗ No unscheduled tasks available")
            return False
        
        device = torch.device('cpu')
        
        # Build graph and features
        g = build_hetgraph(
            env.halfDG, env.num_tasks, env.num_robots,
            env.dur.astype(np.float32), 6, np.array(env.loc, dtype=np.int64), 1,
            env.partials, np.array(unsch_tasks, dtype=np.int32),
            0, np.array(unsch_tasks, dtype=np.int32)
        ).to(device)
        
        feat_dict = hetgraph_node_helper(
            env.halfDG.number_of_nodes(), env.partialw, env.partials,
            env.loc, env.dur, 6, env.num_robots, len(unsch_tasks)
        )
        
        feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) 
                       for k, v in feat_dict.items()}
        
        # Forward pass
        with torch.no_grad():
            outputs = policy_net(g, feat_tensors)
            q_values = outputs['value'].cpu().numpy().reshape(-1)
        
        print(f"✓ Inference successful: {len(q_values)} Q-values")
        print(f"  Q-values range: [{q_values.min():.3f}, {q_values.max():.3f}]")
        
        # Test task selection
        best_idx = np.argmax(q_values)
        best_task = unsch_tasks[best_idx]
        print(f"✓ Best task selected: {best_task} (Q={q_values[best_idx]:.3f})")
        
        return True
        
    except Exception as e:
        print(f"✗ Inference failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all diagnostic tests."""
    print("Multi-Objective Testing Diagnostic Tool")
    print("=" * 50)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Environment Loading", test_environment_loading),
        ("Model Loading", lambda: test_model_loading()[0]),
        ("Graph Building", test_graph_building),
        ("Simple Inference", test_simple_inference)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                print(f"✓ {test_name}: PASSED")
            else:
                print(f"✗ {test_name}: FAILED")
        except Exception as e:
            print(f"✗ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:20s}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your testing environment should work correctly.")
        print("\nTry running:")
        print("python3 test_multi_objective_model_fixed.py --max-instances 3")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please fix the issues above.")


if __name__ == "__main__":
    main()
