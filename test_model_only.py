#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_model_only.py

Simple test script that focuses only on testing the trained multi-objective model
without baseline comparisons to avoid import issues.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import torch

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import ScheduleNet4Layer
from multi_objective_utils import calculate_workload_balance


def load_model(checkpoint_path, device):
    """Load the trained model."""
    # Network architecture
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
    policy_net.eval()
    
    alpha = checkpoint.get('alpha', 0.5)
    beta = checkpoint.get('beta', 0.5)
    
    return policy_net, alpha, beta


def solve_with_model(prefix, num_tasks, num_robots, policy_net, device, alpha=0.5, beta=0.5):
    """Solve using the trained model."""
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
        env.set_multi_objective_params(alpha=alpha, beta=beta)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception as e:
        print(f"Error loading environment: {e}")
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True
    
    map_width = 6
    loc_dist_threshold = 1
    
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break
        
        best_r, best_t, best_q = None, None, -float("inf")
        
        # Evaluate each robot choice
        for r in range(num_robots):
            try:
                # Build heterogeneous graph
                g = build_hetgraph(
                    env.halfDG,
                    num_tasks,
                    num_robots,
                    env.dur,
                    map_width,
                    np.array(env.loc, dtype=np.int64),
                    loc_dist_threshold,
                    env.partials,
                    unsch_tasks,
                    r,
                    unsch_tasks
                ).to(device)
                
                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(),
                    env.partialw,
                    env.partials,
                    env.loc,
                    env.dur,
                    map_width,
                    num_robots,
                    len(unsch_tasks)
                )
                
                feat_tensors = {k: torch.tensor(v, device=device).float() for k, v in feat_dict.items()}
                
                # Forward pass
                with torch.no_grad():
                    outputs = policy_net(g, feat_tensors)
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                
                # Find best task for this robot
                idx = np.argmax(q_values)
                if q_values[idx] > best_q:
                    best_q = float(q_values[idx])
                    best_r = r
                    best_t = int(unsch_tasks[idx])
                    
            except Exception as e:
                print(f"Error evaluating robot {r}: {e}")
                continue
        
        if best_r is None or best_t is None:
            feasible_flag = False
            break
        
        # Execute best action
        success, _, done_flag = env.insert_robot(best_t, best_r)
        if not success:
            feasible_flag = False
            break
        
        assignments[best_r].append(best_t)
        
        if done_flag:
            break
    
    runtime = time.time() - t0
    
    if not feasible_flag:
        return float("nan"), float("nan"), {}, runtime, False
    
    # Calculate final metrics
    ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
    if not ok_final:
        return float("nan"), float("nan"), {}, runtime, False
    
    workload_balance = calculate_workload_balance(assignments, num_robots)
    
    return final_makespan, workload_balance, assignments, runtime, True


def main():
    parser = argparse.ArgumentParser(description="Test multi-objective model only")
    parser.add_argument("--model-path", required=True, help="Path to trained model checkpoint")
    parser.add_argument("--test-data", required=True, help="Path to test data directory")
    parser.add_argument("--num-tasks", type=int, required=True, help="Number of tasks")
    parser.add_argument("--num-robots", type=int, required=True, help="Number of robots")
    parser.add_argument("--max-instances", type=int, default=10, help="Maximum instances to test")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    
    args = parser.parse_args()
    
    device = torch.device(args.device)
    
    print("Multi-Objective Model Testing (Model Only)")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Test data: {args.test_data}")
    print(f"Problem size: {args.num_tasks} tasks, {args.num_robots} robots")
    print(f"Max instances: {args.max_instances}")
    
    # Load model
    try:
        policy_net, alpha, beta = load_model(args.model_path, device)
        print(f"✓ Model loaded successfully")
        print(f"✓ Training parameters: α={alpha:.3f}, β={beta:.3f}")
    except Exception as e:
        print(f"✗ Failed to load model: {e}")
        return
    
    # Test instances
    results = []
    successful_instances = 0
    
    for inst_id in range(1, args.max_instances + 1):
        prefix = os.path.join(args.test_data, f"{inst_id:05d}")
        
        # Check if instance exists
        if not os.path.isfile(f"{prefix}_dur.txt"):
            print(f"[Instance {inst_id:05d}] Files not found, skipping")
            continue
        
        print(f"[Instance {inst_id:05d}] Testing...")
        
        try:
            mk, balance, assigns, rt, ok = solve_with_model(
                prefix, args.num_tasks, args.num_robots, policy_net, device, alpha, beta
            )
            
            if ok:
                successful_instances += 1
                print(f"  ✓ SUCCESS: makespan={mk:.2f}, balance={balance:.3f}, time={rt:.3f}s")
                
                # Show assignment details
                for robot_id, tasks in assigns.items():
                    print(f"    Robot {robot_id}: {tasks} ({len(tasks)} tasks)")
            else:
                print(f"  ✗ FAILED: infeasible solution")
            
            results.append({
                'instance_id': f"{inst_id:05d}",
                'makespan': mk,
                'workload_balance': balance,
                'runtime': rt,
                'feasible': ok
            })
            
        except Exception as e:
            print(f"  ✗ ERROR: {e}")
            results.append({
                'instance_id': f"{inst_id:05d}",
                'makespan': float('nan'),
                'workload_balance': float('nan'),
                'runtime': 0.0,
                'feasible': False
            })
    
    # Summary
    print("\n" + "=" * 50)
    print("RESULTS SUMMARY")
    print("=" * 50)
    
    if results:
        df = pd.DataFrame(results)
        feasible_results = df[df['feasible'] == True]
        
        print(f"Total instances tested: {len(results)}")
        print(f"Successful instances: {successful_instances}")
        print(f"Success rate: {successful_instances/len(results)*100:.1f}%")
        
        if len(feasible_results) > 0:
            print(f"\nPerformance on successful instances:")
            print(f"  Average makespan: {feasible_results['makespan'].mean():.2f}")
            print(f"  Average workload balance: {feasible_results['workload_balance'].mean():.3f}")
            print(f"  Average runtime: {feasible_results['runtime'].mean():.3f}s")
            print(f"  Best makespan: {feasible_results['makespan'].min():.2f}")
            print(f"  Best workload balance: {feasible_results['workload_balance'].min():.3f}")
            
            # Save results
            output_file = "model_test_results.csv"
            df.to_csv(output_file, index=False)
            print(f"\nResults saved to: {output_file}")
        else:
            print("\nNo successful instances to analyze.")
    else:
        print("No instances were tested.")
    
    print("\nTesting completed!")


if __name__ == "__main__":
    main()
