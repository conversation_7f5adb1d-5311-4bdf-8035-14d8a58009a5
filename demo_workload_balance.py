#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
demo_workload_balance.py

Simple demonstration of workload balance optimization features.
This script demonstrates the core concepts without requiring <PERSON><PERSON><PERSON>.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List


def calculate_workload_balance(assignments: Dict[int, List[int]], num_robots: int) -> float:
    """Calculate workload balance metric (standard deviation of task counts)."""
    task_counts = np.zeros(num_robots)
    for robot_id, tasks in assignments.items():
        if robot_id < num_robots:
            task_counts[robot_id] = len(tasks)
    return np.std(task_counts)


def demonstrate_workload_balance_calculation():
    """Demonstrate workload balance calculation with different scenarios."""
    print("=== Workload Balance Calculation Demo ===")
    
    # Scenario 1: Perfectly balanced
    print("\n1. Perfectly Balanced Assignment:")
    assignments1 = {0: [1, 2, 3], 1: [4, 5, 6]}
    balance1 = calculate_workload_balance(assignments1, 2)
    print(f"   Robot 0: tasks {assignments1[0]} (count: {len(assignments1[0])})")
    print(f"   Robot 1: tasks {assignments1[1]} (count: {len(assignments1[1])})")
    print(f"   Workload Balance Score: {balance1:.3f}")
    
    # Scenario 2: Slightly imbalanced
    print("\n2. Slightly Imbalanced Assignment:")
    assignments2 = {0: [1, 2, 3, 4], 1: [5, 6]}
    balance2 = calculate_workload_balance(assignments2, 2)
    print(f"   Robot 0: tasks {assignments2[0]} (count: {len(assignments2[0])})")
    print(f"   Robot 1: tasks {assignments2[1]} (count: {len(assignments2[1])})")
    print(f"   Workload Balance Score: {balance2:.3f}")
    
    # Scenario 3: Highly imbalanced
    print("\n3. Highly Imbalanced Assignment:")
    assignments3 = {0: [1, 2, 3, 4, 5], 1: [6]}
    balance3 = calculate_workload_balance(assignments3, 2)
    print(f"   Robot 0: tasks {assignments3[0]} (count: {len(assignments3[0])})")
    print(f"   Robot 1: tasks {assignments3[1]} (count: {len(assignments3[1])})")
    print(f"   Workload Balance Score: {balance3:.3f}")
    
    # Scenario 4: One robot idle
    print("\n4. One Robot Idle:")
    assignments4 = {0: [1, 2, 3, 4, 5, 6], 1: []}
    balance4 = calculate_workload_balance(assignments4, 2)
    print(f"   Robot 0: tasks {assignments4[0]} (count: {len(assignments4[0])})")
    print(f"   Robot 1: tasks {assignments4[1]} (count: {len(assignments4[1])})")
    print(f"   Workload Balance Score: {balance4:.3f}")
    
    print(f"\nBalance scores increase with imbalance: {balance1:.3f} < {balance2:.3f} < {balance3:.3f} < {balance4:.3f}")


def demonstrate_multi_objective_tradeoffs():
    """Demonstrate trade-offs between makespan and workload balance."""
    print("\n=== Multi-Objective Trade-offs Demo ===")
    
    # Simulate different scheduling solutions
    solutions = [
        # (makespan, workload_balance, description)
        (10.0, 0.0, "Perfect balance, longer makespan"),
        (8.0, 1.0, "Good balance, medium makespan"),
        (6.0, 2.0, "Poor balance, shorter makespan"),
        (12.0, 0.5, "Good balance, long makespan"),
        (7.0, 1.5, "Medium balance, medium makespan"),
        (5.0, 3.0, "Very poor balance, very short makespan"),
    ]
    
    print("\nDifferent scheduling solutions:")
    for i, (mk, bal, desc) in enumerate(solutions, 1):
        print(f"{i}. {desc}")
        print(f"   Makespan: {mk:.1f}, Workload Balance: {bal:.1f}")
    
    # Calculate Pareto front
    pareto_front = []
    for i, (mk1, bal1, desc1) in enumerate(solutions):
        is_dominated = False
        for j, (mk2, bal2, desc2) in enumerate(solutions):
            if i != j:
                # Check if solution i is dominated by solution j
                if (mk2 <= mk1 and bal2 <= bal1) and (mk2 < mk1 or bal2 < bal1):
                    is_dominated = True
                    break
        if not is_dominated:
            pareto_front.append((mk1, bal1, desc1))
    
    print(f"\nPareto Front (non-dominated solutions): {len(pareto_front)} out of {len(solutions)}")
    for mk, bal, desc in pareto_front:
        print(f"   - {desc} (Makespan: {mk:.1f}, Balance: {bal:.1f})")


def demonstrate_weighted_objectives():
    """Demonstrate weighted combination of objectives."""
    print("\n=== Weighted Objective Combination Demo ===")
    
    # Example solutions
    makespan = 10.0
    workload_balance = 2.0
    
    # Different weight combinations
    weight_combinations = [
        (1.0, 0.0, "Makespan only"),
        (0.8, 0.2, "Favor makespan"),
        (0.5, 0.5, "Equal weights"),
        (0.2, 0.8, "Favor workload balance"),
        (0.0, 1.0, "Workload balance only"),
    ]
    
    print(f"For a solution with Makespan={makespan}, Workload Balance={workload_balance}:")
    print("Weight combinations and resulting objective values:")
    
    # Normalization weights (for demonstration)
    makespan_weight = 15.0  # Typical max makespan
    balance_weight = 3.0    # Typical max balance
    
    for alpha, beta, description in weight_combinations:
        # Calculate weighted objective
        normalized_makespan = makespan / makespan_weight
        normalized_balance = workload_balance / balance_weight
        weighted_obj = alpha * normalized_makespan + beta * normalized_balance
        
        print(f"   {description:20s} (α={alpha:.1f}, β={beta:.1f}): {weighted_obj:.3f}")


def demonstrate_assignment_strategies():
    """Demonstrate different task assignment strategies."""
    print("\n=== Task Assignment Strategies Demo ===")
    
    num_tasks = 8
    num_robots = 3
    tasks = list(range(1, num_tasks + 1))
    
    # Strategy 1: Round-robin
    print("\n1. Round-Robin Assignment:")
    round_robin = {r: [] for r in range(num_robots)}
    for i, task in enumerate(tasks):
        robot = i % num_robots
        round_robin[robot].append(task)
    
    for robot, robot_tasks in round_robin.items():
        print(f"   Robot {robot}: {robot_tasks}")
    balance_rr = calculate_workload_balance(round_robin, num_robots)
    print(f"   Workload Balance: {balance_rr:.3f}")
    
    # Strategy 2: Load-based (try to balance)
    print("\n2. Load-Balanced Assignment:")
    load_balanced = {r: [] for r in range(num_robots)}
    task_counts = [0] * num_robots
    
    for task in tasks:
        # Assign to robot with minimum current load
        min_robot = min(range(num_robots), key=lambda r: task_counts[r])
        load_balanced[min_robot].append(task)
        task_counts[min_robot] += 1
    
    for robot, robot_tasks in load_balanced.items():
        print(f"   Robot {robot}: {robot_tasks}")
    balance_lb = calculate_workload_balance(load_balanced, num_robots)
    print(f"   Workload Balance: {balance_lb:.3f}")
    
    # Strategy 3: Random assignment
    print("\n3. Random Assignment:")
    np.random.seed(42)  # For reproducibility
    random_assignment = {r: [] for r in range(num_robots)}
    for task in tasks:
        robot = np.random.randint(0, num_robots)
        random_assignment[robot].append(task)
    
    for robot, robot_tasks in random_assignment.items():
        print(f"   Robot {robot}: {robot_tasks}")
    balance_rand = calculate_workload_balance(random_assignment, num_robots)
    print(f"   Workload Balance: {balance_rand:.3f}")
    
    print(f"\nComparison of balance scores:")
    print(f"   Round-Robin: {balance_rr:.3f}")
    print(f"   Load-Balanced: {balance_lb:.3f}")
    print(f"   Random: {balance_rand:.3f}")


def main():
    """Run all demonstrations."""
    print("Workload Balance Optimization Demonstration")
    print("=" * 50)
    
    demonstrate_workload_balance_calculation()
    demonstrate_multi_objective_tradeoffs()
    demonstrate_weighted_objectives()
    demonstrate_assignment_strategies()
    
    print("\n" + "=" * 50)
    print("Demonstration completed!")
    print("\nKey Takeaways:")
    print("1. Workload balance is measured as standard deviation of task counts")
    print("2. Lower balance scores indicate more balanced workloads")
    print("3. Multi-objective optimization involves trade-offs between makespan and balance")
    print("4. Different assignment strategies achieve different balance levels")
    print("5. Weighted objectives allow tuning the importance of each goal")


if __name__ == "__main__":
    main()
