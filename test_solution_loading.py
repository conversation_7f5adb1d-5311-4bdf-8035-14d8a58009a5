#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_solution_loading.py

Test script to verify that solution files can be loaded correctly.
"""

import os
import numpy as np
from utils import SchedulingEnv


def test_solution_loading():
    """Test loading solution files for training."""
    print("=== Testing Solution File Loading ===")
    
    # Test parameters
    data_dir = "./problem_instances/constraints"
    solutions_dir = "./problem_instances/solutions"
    num_robots = 2
    test_instances = [1, 2, 3]
    
    for inst_no in test_instances:
        print(f"\nTesting instance {inst_no:05d}:")
        
        # Check constraint files
        fname = os.path.join(data_dir, f"{inst_no:05d}")
        if not os.path.isfile(f"{fname}_dur.txt"):
            print(f"  ✗ Constraint files not found: {fname}_dur.txt")
            continue
        
        print(f"  ✓ Constraint files found: {fname}_dur.txt")
        
        # Load environment
        try:
            env = SchedulingEnv(fname)
            env.set_multi_objective_params(alpha=0.5, beta=0.5)
            print(f"  ✓ Environment loaded: {env.num_tasks} tasks, {env.num_robots} robots")
        except Exception as e:
            print(f"  ✗ Failed to load environment: {e}")
            continue
        
        # Check solution files
        solution_prefix = os.path.join(solutions_dir, f"{inst_no:05d}")
        
        # Load robot solutions
        optimals = []
        for i in range(num_robots):
            opt_file = f"{solution_prefix}_{i}.txt"
            if os.path.isfile(opt_file):
                try:
                    optimal = np.loadtxt(opt_file, dtype=np.int32)
                    optimals.append(optimal)
                    print(f"  ✓ Robot {i} solution loaded: {len(optimal)} tasks")
                except Exception as e:
                    print(f"  ✗ Failed to load robot {i} solution: {e}")
            else:
                print(f"  ✗ Robot {i} solution file not found: {opt_file}")
        
        # Load task sequence
        optimalw_file = f"{solution_prefix}_w.txt"
        if os.path.isfile(optimalw_file):
            try:
                optimalw = np.loadtxt(optimalw_file, dtype=np.int32)
                print(f"  ✓ Task sequence loaded: {len(optimalw)} tasks")
                print(f"    Sequence: {optimalw}")
            except Exception as e:
                print(f"  ✗ Failed to load task sequence: {e}")
        else:
            print(f"  ✗ Task sequence file not found: {optimalw_file}")
        
        # Test trajectory execution
        if len(optimals) == num_robots and os.path.isfile(optimalw_file):
            print(f"  Testing trajectory execution...")
            try:
                # Reset environment
                env = SchedulingEnv(fname)
                env.set_multi_objective_params(alpha=0.5, beta=0.5)
                
                rewards = []
                for i, task in enumerate(optimalw):
                    # Find which robot should execute this task
                    robot = None
                    for r in range(num_robots):
                        if task in optimals[r]:
                            robot = r
                            break
                    
                    if robot is None:
                        print(f"    ✗ Task {task} not found in any robot's solution")
                        break
                    
                    # Execute action
                    success, reward, done_flag = env.insert_robot(task, robot)
                    if not success:
                        print(f"    ✗ Failed to insert task {task} into robot {robot}")
                        break
                    
                    rewards.append(reward)
                    print(f"    Step {i+1}: Task {task} → Robot {robot}, Reward: {reward:.4f}")
                
                if len(rewards) == len(optimalw):
                    print(f"  ✓ Trajectory executed successfully!")
                    print(f"    Total rewards: {len(rewards)}")
                    print(f"    Average reward: {np.mean(rewards):.4f}")
                    print(f"    Non-zero rewards: {sum(1 for r in rewards if r != 0)}")
                else:
                    print(f"  ✗ Trajectory execution incomplete")
                    
            except Exception as e:
                print(f"  ✗ Error during trajectory execution: {e}")


def test_multi_objective_reward():
    """Test multi-objective reward calculation."""
    print("\n=== Testing Multi-Objective Reward Calculation ===")
    
    # Test with first available instance
    data_dir = "./problem_instances/constraints"
    
    for inst_no in range(1, 6):
        fname = os.path.join(data_dir, f"{inst_no:05d}")
        if os.path.isfile(f"{fname}_dur.txt"):
            print(f"\nTesting with instance {inst_no:05d}:")
            
            try:
                # Load environment
                env = SchedulingEnv(fname)
                print(f"  Environment: {env.num_tasks} tasks, {env.num_robots} robots")
                
                # Test single-objective mode
                print(f"  Single-objective mode:")
                initial_balance = env.calculate_workload_balance()
                print(f"    Initial workload balance: {initial_balance:.3f}")
                
                # Test multi-objective mode
                print(f"  Multi-objective mode:")
                env.set_multi_objective_params(alpha=0.6, beta=0.4)
                print(f"    Alpha: {env.alpha}, Beta: {env.beta}")
                print(f"    Multi-objective enabled: {env.use_multi_objective}")
                
                # Test inserting a task
                unsch_tasks = env.get_unscheduled_tasks()
                if len(unsch_tasks) > 0:
                    task = unsch_tasks[0]
                    robot = 0
                    
                    print(f"    Testing task insertion: Task {task} → Robot {robot}")
                    success, reward, done_flag = env.insert_robot(task, robot)
                    
                    if success:
                        print(f"    ✓ Insertion successful, Reward: {reward:.4f}")
                        new_balance = env.calculate_workload_balance()
                        print(f"    New workload balance: {new_balance:.3f}")
                    else:
                        print(f"    ✗ Insertion failed")
                
                return True  # Success, exit after first working instance
                
            except Exception as e:
                print(f"  ✗ Error: {e}")
                continue
    
    print("  ✗ No working instances found")
    return False


def main():
    """Run all tests."""
    print("Solution Loading and Multi-Objective Test Suite")
    print("=" * 50)
    
    test_solution_loading()
    test_multi_objective_reward()
    
    print("\n" + "=" * 50)
    print("Testing completed!")
    print("\nIf you see non-zero rewards above, the training should work correctly.")
    print("If all rewards are zero, there may be an issue with the reward calculation.")


if __name__ == "__main__":
    main()
