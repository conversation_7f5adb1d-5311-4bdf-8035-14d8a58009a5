#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_workload_balance.py

Test script to verify workload balance optimization implementation.
"""

import os
import numpy as np
from utils import SchedulingEnv
from multi_objective_utils import calculate_workload_balance, MultiObjectiveMetrics

# Try to import Gurobi-based solvers, but handle gracefully if not available
try:
    from workload_balance_solvers import (solve_workload_balance_gurobi,
                                         solve_multi_objective_gurobi,
                                         solve_workload_balance_heuristic)
    from makespan_for_each import solve_with_gurobi
    GUROBI_AVAILABLE = True
except ImportError as e:
    print(f"Gurobi not available: {e}")
    GUROBI_AVAILABLE = False


def test_workload_balance_calculation():
    """Test the workload balance calculation function."""
    print("=== Testing Workload Balance Calculation ===")
    
    # Test case 1: Perfectly balanced
    assignments1 = {0: [1, 2], 1: [3, 4]}
    balance1 = calculate_workload_balance(assignments1, 2)
    print(f"Perfectly balanced (2 tasks each): {balance1:.3f}")
    
    # Test case 2: Imbalanced
    assignments2 = {0: [1, 2, 3], 1: [4]}
    balance2 = calculate_workload_balance(assignments2, 2)
    print(f"Imbalanced (3 vs 1 tasks): {balance2:.3f}")
    
    # Test case 3: One robot has no tasks
    assignments3 = {0: [1, 2, 3, 4], 1: []}
    balance3 = calculate_workload_balance(assignments3, 2)
    print(f"One robot idle (4 vs 0 tasks): {balance3:.3f}")
    
    assert balance1 < balance2 < balance3, "Balance scores should increase with imbalance"
    print("✓ Workload balance calculation test passed\n")


def test_multi_objective_metrics():
    """Test the MultiObjectiveMetrics class."""
    print("=== Testing Multi-Objective Metrics ===")
    
    metrics = MultiObjectiveMetrics()
    
    # Add some test solutions
    metrics.add_solution(10.0, 1.0, {'solver': 'test1'})  # Good makespan, good balance
    metrics.add_solution(15.0, 0.5, {'solver': 'test2'})  # Worse makespan, better balance
    metrics.add_solution(8.0, 2.0, {'solver': 'test3'})   # Better makespan, worse balance
    metrics.add_solution(12.0, 1.5, {'solver': 'test4'})  # Dominated solution
    
    # Get Pareto front
    pareto_front = metrics.get_pareto_front()
    print(f"Number of solutions: {len(metrics.solutions)}")
    print(f"Pareto front size: {len(pareto_front)}")
    
    # Check that dominated solution is not in Pareto front
    pareto_makespans = [sol[0] for sol in pareto_front]
    assert 12.0 not in pareto_makespans, "Dominated solution should not be in Pareto front"
    
    # Get statistics
    stats = metrics.get_statistics()
    print(f"Statistics: {stats}")
    
    print("✓ Multi-objective metrics test passed\n")


def test_with_real_instance():
    """Test with a real problem instance if available."""
    print("=== Testing with Real Instance ===")
    
    # Look for test data
    test_data_paths = [
        "./problem_instances_test/constraints",
        "./problem_instances/constraints", 
        "./data"
    ]
    
    test_instance = None
    for path in test_data_paths:
        if os.path.exists(path):
            # Look for the first available instance
            for i in range(1, 10):
                prefix = os.path.join(path, f"{i:05d}")
                if os.path.isfile(f"{prefix}_dur.txt"):
                    test_instance = prefix
                    break
            if test_instance:
                break
    
    if not test_instance:
        # Try the specific data file mentioned in the codebase
        if os.path.exists("./data/00374_dur.txt"):
            test_instance = "./data/00374"
        else:
            print("No test instance found, skipping real instance test")
            return
    
    print(f"Testing with instance: {test_instance}")
    
    try:
        # Load environment to get problem size
        env = SchedulingEnv(test_instance)
        num_tasks = env.num_tasks
        num_robots = env.num_robots
        
        print(f"Problem size: {num_tasks} tasks, {num_robots} robots")
        
        # Test different solvers
        results = {}

        if GUROBI_AVAILABLE:
            # 1. Standard makespan optimization
            try:
                mk, assigns, rt, ok = solve_with_gurobi(test_instance, num_tasks, num_robots)
                if ok:
                    balance = calculate_workload_balance(assigns, num_robots)
                    results['makespan_only'] = (mk, balance, rt)
                    print(f"Makespan-only: makespan={mk:.2f}, balance={balance:.2f}, time={rt:.2f}s")
            except Exception as e:
                print(f"Makespan-only solver failed: {e}")

            # 2. Workload balance optimization
            try:
                balance, assigns, rt, ok = solve_workload_balance_gurobi(test_instance, num_tasks, num_robots)
                if ok:
                    # Calculate makespan for this solution
                    env_test = SchedulingEnv(test_instance)
                    for robot_id, tasks in assigns.items():
                        for task in tasks:
                            env_test.insert_robot(task, robot_id)
                    _, makespan = env_test.check_consistency_makespan(updateDG=False)
                    results['balance_only'] = (makespan, balance, rt)
                    print(f"Balance-only: makespan={makespan:.2f}, balance={balance:.2f}, time={rt:.2f}s")
            except Exception as e:
                print(f"Balance-only solver failed: {e}")

            # 3. Multi-objective optimization
            try:
                mk, balance, assigns, rt, ok = solve_multi_objective_gurobi(
                    test_instance, num_tasks, num_robots, alpha=0.5, beta=0.5
                )
                if ok:
                    results['multi_objective'] = (mk, balance, rt)
                    print(f"Multi-objective: makespan={mk:.2f}, balance={balance:.2f}, time={rt:.2f}s")
            except Exception as e:
                print(f"Multi-objective solver failed: {e}")

            # 4. Heuristic balance optimization
            try:
                balance, assigns, rt, ok = solve_workload_balance_heuristic(test_instance, num_tasks, num_robots)
                if ok:
                    # Calculate makespan for this solution
                    env_test = SchedulingEnv(test_instance)
                    for robot_id, tasks in assigns.items():
                        for task in tasks:
                            env_test.insert_robot(task, robot_id)
                    _, makespan = env_test.check_consistency_makespan(updateDG=False)
                    results['balance_heuristic'] = (makespan, balance, rt)
                    print(f"Balance heuristic: makespan={makespan:.2f}, balance={balance:.2f}, time={rt:.2f}s")
            except Exception as e:
                print(f"Balance heuristic solver failed: {e}")
        else:
            print("Gurobi not available - testing basic workload balance calculation only")

            # Test basic workload balance calculation with manual assignments
            # Create a simple round-robin assignment for testing
            assignments = {r: [] for r in range(num_robots)}
            for task_id in range(1, num_tasks + 1):
                robot_id = (task_id - 1) % num_robots
                assignments[robot_id].append(task_id)

            balance = calculate_workload_balance(assignments, num_robots)
            print(f"Round-robin assignment balance: {balance:.2f}")
            results['round_robin'] = (float('nan'), balance, 0.0)
        
        if results:
            print("✓ Real instance test completed successfully")
            
            # Show trade-offs
            print("\n--- Trade-off Analysis ---")
            for solver, (mk, bal, rt) in results.items():
                print(f"{solver:20s}: Makespan={mk:6.2f}, Balance={bal:6.2f}")
        else:
            print("✗ No solvers succeeded on real instance")
            
    except Exception as e:
        print(f"Real instance test failed: {e}")
    
    print()


def main():
    """Run all tests."""
    print("Starting workload balance optimization tests...\n")
    
    test_workload_balance_calculation()
    test_multi_objective_metrics()
    test_with_real_instance()
    
    print("All tests completed!")


if __name__ == "__main__":
    main()
