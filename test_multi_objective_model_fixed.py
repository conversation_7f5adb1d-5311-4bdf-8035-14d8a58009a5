#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_multi_objective_model_fixed.py

Fixed version of the multi-objective model testing script that handles
baseline solver compatibility issues.
"""

import os
import argparse
import time
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

from utils import SchedulingEnv, build_hetgraph, hetgraph_node_helper
from hetnet import ScheduleNet4Layer
from multi_objective_utils import (MultiObjectiveMetrics, calculate_workload_balance,
                                  calculate_workload_balance_from_env)


def load_multi_objective_model(checkpoint_path: str, device: torch.device):
    """Load the trained multi-objective model."""
    # Network architecture
    in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
    hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
    out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
    cetypes = [
        ('task', 'temporal', 'task'),
        ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
        ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
        ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
        ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
        ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
        ('state', 'sto', 'value'), ('value', 'vto', 'value'),
        ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
    ]
    
    policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
    policy_net.eval()
    
    alpha = checkpoint.get('alpha', 0.5)
    beta = checkpoint.get('beta', 0.5)
    
    return policy_net, alpha, beta


def solve_with_multi_objective_ssan(prefix: str, num_tasks: int, num_robots: int, 
                                   policy_net, device: torch.device, 
                                   alpha: float = 0.5, beta: float = 0.5) -> Tuple[float, float, Dict, float, bool]:
    """Solve using multi-objective trained SSAN model."""
    t0 = time.time()
    
    try:
        env = SchedulingEnv(prefix)
        env.set_multi_objective_params(alpha=alpha, beta=beta)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception as e:
        print(f"Error loading environment: {e}")
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True
    
    map_width = 6
    loc_dist_threshold = 1
    
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break
        
        best_r, best_t, best_q = None, None, -float("inf")
        
        # Evaluate each robot choice
        for r in range(num_robots):
            try:
                # Build heterogeneous graph
                g = build_hetgraph(
                    env.halfDG,
                    num_tasks,
                    num_robots,
                    env.dur.astype(np.float32),
                    map_width,
                    np.array(env.loc, dtype=np.int64),
                    loc_dist_threshold,
                    env.partials,
                    np.array(unsch_tasks, dtype=np.int32),
                    r,
                    np.array(unsch_tasks, dtype=np.int32)
                ).to(device)
                
                # Build features
                feat_dict = hetgraph_node_helper(
                    env.halfDG.number_of_nodes(),
                    env.partialw,
                    env.partials,
                    env.loc,
                    env.dur,
                    map_width,
                    num_robots,
                    len(unsch_tasks)
                )
                
                feat_tensors = {k: torch.tensor(v, device=device, dtype=torch.float32) for k, v in feat_dict.items()}
                
                # Forward pass
                with torch.no_grad():
                    outputs = policy_net(g, feat_tensors)
                    q_values = outputs['value'].cpu().numpy().reshape(-1)
                
                # Find best task for this robot
                idx = np.argmax(q_values)
                if q_values[idx] > best_q:
                    best_q = float(q_values[idx])
                    best_r = r
                    best_t = int(unsch_tasks[idx])
                    
            except Exception as e:
                print(f"Error evaluating robot {r}: {e}")
                continue
        
        if best_r is None or best_t is None:
            feasible_flag = False
            break
        
        # Execute best action
        success, _, done_flag = env.insert_robot(best_t, best_r)
        if not success:
            feasible_flag = False
            break
        
        assignments[best_r].append(best_t)
        
        if done_flag:
            break
    
    runtime = time.time() - t0
    
    if not feasible_flag:
        return float("nan"), float("nan"), {}, runtime, False
    
    # Calculate final metrics
    ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
    if not ok_final:
        return float("nan"), float("nan"), {}, runtime, False
    
    workload_balance = calculate_workload_balance(assignments, num_robots)
    
    return final_makespan, workload_balance, assignments, runtime, True


def solve_with_simple_baseline(prefix: str, num_tasks: int, num_robots: int, 
                              strategy: str = "random") -> Tuple[float, float, Dict, float, bool]:
    """
    Simple baseline solver that works with multi-objective environment.
    
    Args:
        prefix: Problem instance prefix
        num_tasks: Number of tasks
        num_robots: Number of robots
        strategy: "random", "round_robin", or "greedy"
    """
    t0 = time.time()
    
    try:
        # Use standard environment (no multi-objective) for baseline
        env = SchedulingEnv(prefix)
        ok, mm = env.check_consistency_makespan(updateDG=False)
        if not ok:
            return float("nan"), float("nan"), {}, time.time() - t0, False
        env.min_makespan = mm
    except Exception as e:
        return float("nan"), float("nan"), {}, time.time() - t0, False
    
    assignments = {r: [] for r in range(num_robots)}
    feasible_flag = True
    
    while True:
        unsch_tasks = env.get_unscheduled_tasks()
        if len(unsch_tasks) == 0:
            break
        
        # Choose task and robot based on strategy
        if strategy == "random":
            task = np.random.choice(unsch_tasks)
            robot = np.random.randint(0, num_robots)
        elif strategy == "round_robin":
            task = unsch_tasks[0]  # First available task
            robot = len(env.partialw) % num_robots  # Round-robin robot assignment
        elif strategy == "greedy":
            # Choose task with minimum duration on any robot
            task = unsch_tasks[0]
            min_dur = float('inf')
            best_robot = 0
            for t in unsch_tasks:
                for r in range(num_robots):
                    dur = env.dur[t-1, r]
                    if dur < min_dur:
                        min_dur = dur
                        task = t
                        best_robot = r
            robot = best_robot
        else:
            # Default to random
            task = np.random.choice(unsch_tasks)
            robot = np.random.randint(0, num_robots)
        
        # Execute action
        success, _, done_flag = env.insert_robot(task, robot)
        if not success:
            feasible_flag = False
            break
        
        assignments[robot].append(task)
        
        if done_flag:
            break
    
    runtime = time.time() - t0
    
    if not feasible_flag:
        return float("nan"), float("nan"), {}, runtime, False
    
    # Calculate final metrics
    ok_final, final_makespan = env.check_consistency_makespan(updateDG=False)
    if not ok_final:
        return float("nan"), float("nan"), {}, runtime, False
    
    workload_balance = calculate_workload_balance(assignments, num_robots)
    
    return final_makespan, workload_balance, assignments, runtime, True


def evaluate_model_on_instances(model_path: str, test_data_path: str, 
                               num_tasks: int, num_robots: int, 
                               max_instances: int, device: torch.device) -> Tuple[pd.DataFrame, MultiObjectiveMetrics]:
    """Evaluate multi-objective model on test instances."""
    print(f"Loading model from: {model_path}")
    policy_net, alpha, beta = load_multi_objective_model(model_path, device)
    print(f"Model trained with α={alpha:.3f}, β={beta:.3f}")
    
    results = []
    metrics = MultiObjectiveMetrics()
    
    for inst_id in range(1, max_instances + 1):
        prefix = os.path.join(test_data_path, f"{inst_id:05d}")
        
        # Check if instance exists
        if not os.path.isfile(f"{prefix}_dur.txt"):
            print(f"[Instance {inst_id:05d}] Files not found, skipping")
            continue
        
        print(f"[Instance {inst_id:05d}] Evaluating...")
        
        # Test different solvers
        solvers = {
            'multi_obj_ssan': lambda: solve_with_multi_objective_ssan(
                prefix, num_tasks, num_robots, policy_net, device, alpha, beta
            ),
            'random': lambda: solve_with_simple_baseline(prefix, num_tasks, num_robots, "random"),
            'round_robin': lambda: solve_with_simple_baseline(prefix, num_tasks, num_robots, "round_robin"),
            'greedy': lambda: solve_with_simple_baseline(prefix, num_tasks, num_robots, "greedy")
        }
        
        for solver_name, solver_func in solvers.items():
            try:
                mk, balance, assigns, rt, ok = solver_func()
                
                # Store results
                result = {
                    'instance_id': f"{inst_id:05d}",
                    'solver': solver_name,
                    'makespan': mk,
                    'workload_balance': balance,
                    'runtime': rt,
                    'feasible': ok
                }
                results.append(result)
                
                # Add to metrics if feasible
                if ok and not np.isnan(mk) and not np.isnan(balance):
                    metrics.add_solution(mk, balance, {'solver': solver_name, 'instance': inst_id})
                
                print(f"  {solver_name:15s}: makespan={mk:6.2f}, balance={balance:6.3f}, "
                      f"feasible={ok}, time={rt:.2f}s")
                
            except Exception as e:
                print(f"  {solver_name:15s}: ERROR - {e}")
                result = {
                    'instance_id': f"{inst_id:05d}",
                    'solver': solver_name,
                    'makespan': float('nan'),
                    'workload_balance': float('nan'),
                    'runtime': 0.0,
                    'feasible': False
                }
                results.append(result)
    
    return pd.DataFrame(results), metrics


def main():
    parser = argparse.ArgumentParser(description="Test multi-objective trained model (fixed version)")
    parser.add_argument("--model-path", default='./cp_multi_obj/checkpoint_00500.tar', help="Path to trained model checkpoint")
    parser.add_argument("--test-data", default="./problem_instances_test/constraints", help="Path to test data directory")
    parser.add_argument("--num-tasks", default=5, type=int, help="Number of tasks")
    parser.add_argument("--num-robots", default=2, type=int, help="Number of robots")
    parser.add_argument("--max-instances", type=int, default=10, help="Maximum instances to test")
    parser.add_argument("--output-dir", default="./test_results_fixed", help="Output directory")
    parser.add_argument("--device", default="cpu", help="Device (cpu or cuda)")
    
    args = parser.parse_args()
    
    device = torch.device(args.device)
    
    print("Multi-Objective Model Testing (Fixed Version)")
    print("=" * 50)
    print(f"Model: {args.model_path}")
    print(f"Test data: {args.test_data}")
    print(f"Problem size: {args.num_tasks} tasks, {args.num_robots} robots")
    print(f"Max instances: {args.max_instances}")
    
    # Run evaluation
    results_df, metrics = evaluate_model_on_instances(
        args.model_path, args.test_data, args.num_tasks, args.num_robots,
        args.max_instances, device
    )
    
    # Simple analysis
    print("\n=== RESULTS SUMMARY ===")
    
    if not results_df.empty:
        summary_stats = []
        for solver in results_df['solver'].unique():
            solver_data = results_df[results_df['solver'] == solver]
            feasible_data = solver_data[solver_data['feasible'] == True]
            
            stats = {
                'solver': solver,
                'num_feasible': len(feasible_data),
                'num_total': len(solver_data),
                'feasibility_rate': len(feasible_data) / len(solver_data) if len(solver_data) > 0 else 0,
                'avg_makespan': feasible_data['makespan'].mean() if len(feasible_data) > 0 else float('nan'),
                'avg_workload_balance': feasible_data['workload_balance'].mean() if len(feasible_data) > 0 else float('nan'),
                'avg_runtime': feasible_data['runtime'].mean() if len(feasible_data) > 0 else float('nan')
            }
            summary_stats.append(stats)
        
        summary_df = pd.DataFrame(summary_stats)
        print(summary_df.to_string(index=False, float_format='%.3f'))
        
        # Save results
        os.makedirs(args.output_dir, exist_ok=True)
        results_df.to_csv(os.path.join(args.output_dir, 'detailed_results.csv'), index=False)
        summary_df.to_csv(os.path.join(args.output_dir, 'summary_statistics.csv'), index=False)
        
        print(f"\nResults saved to: {args.output_dir}")
        
        # Generate Pareto front plot
        pareto_plot_path = os.path.join(args.output_dir, 'pareto_front.png')
        metrics.plot_pareto_front(save_path=pareto_plot_path)
        
        pareto_front = metrics.get_pareto_front()
        print(f"Pareto front contains {len(pareto_front)} solutions")
    else:
        print("No results to analyze.")
    
    print("\nTesting completed!")


if __name__ == "__main__":
    main()
