#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_simple_multi_objective.py

Simple test to verify multi-objective functionality works correctly.
"""

import os
import numpy as np
from utils import SchedulingEnv
from multi_objective_utils import calculate_workload_balance


def test_environment_setup():
    """Test that we can create and configure a multi-objective environment."""
    print("=== Testing Environment Setup ===")
    
    # Find a test instance
    test_instance = None
    test_paths = [
        "./problem_instances_test/constraints/00001",
        "./problem_instances/constraints/00001",
        "./data/00001"
    ]
    
    for path in test_paths:
        if os.path.isfile(f"{path}_dur.txt"):
            test_instance = path
            break
    
    if not test_instance:
        print("No test instance found. Creating minimal test...")
        return test_minimal_functionality()
    
    print(f"Using test instance: {test_instance}")
    
    try:
        # Create environment
        env = SchedulingEnv(test_instance)
        print(f"✓ Environment created: {env.num_tasks} tasks, {env.num_robots} robots")
        
        # Check default state
        print(f"✓ Default use_multi_objective: {env.use_multi_objective}")
        
        # Test multi-objective setup
        env.set_multi_objective_params(alpha=0.6, beta=0.4)
        print(f"✓ Multi-objective enabled: {env.use_multi_objective}")
        print(f"✓ Alpha: {env.alpha}, Beta: {env.beta}")
        
        # Test workload balance calculation
        initial_balance = env.calculate_workload_balance()
        print(f"✓ Initial workload balance: {initial_balance:.3f}")
        
        # Test unscheduled tasks
        unsch_tasks = env.get_unscheduled_tasks()
        print(f"✓ Unscheduled tasks: {len(unsch_tasks)} tasks")
        
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        return False


def test_minimal_functionality():
    """Test basic functionality without requiring data files."""
    print("\n=== Testing Minimal Functionality ===")
    
    try:
        # Test workload balance calculation
        assignments = {0: [1, 2], 1: [3, 4], 2: [5]}
        balance = calculate_workload_balance(assignments, 3)
        print(f"✓ Workload balance calculation: {balance:.3f}")
        
        # Test different scenarios
        scenarios = [
            ({0: [1, 2], 1: [3, 4]}, 2, "Balanced"),
            ({0: [1, 2, 3], 1: [4]}, 2, "Imbalanced"),
            ({0: [1, 2, 3, 4], 1: []}, 2, "One robot idle"),
        ]
        
        for assignments, num_robots, description in scenarios:
            balance = calculate_workload_balance(assignments, num_robots)
            print(f"✓ {description}: {balance:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in minimal test: {e}")
        return False


def test_model_loading():
    """Test if we can load the trained model."""
    print("\n=== Testing Model Loading ===")
    
    model_path = "./cp_multi_obj/checkpoint_00500.tar"
    
    if not os.path.exists(model_path):
        print(f"Model not found: {model_path}")
        return False
    
    try:
        import torch
        from hetnet import ScheduleNet4Layer
        
        # Network architecture
        in_dim = {'task': 6, 'loc': 1, 'robot': 1, 'state': 4, 'value': 1}
        hid_dim = {'task': 64, 'loc': 64, 'robot': 64, 'state': 64, 'value': 64}
        out_dim = {'task': 32, 'loc': 32, 'robot': 32, 'state': 32, 'value': 1}
        cetypes = [
            ('task', 'temporal', 'task'),
            ('task', 'located_in', 'loc'), ('loc', 'near', 'loc'),
            ('task', 'assigned_to', 'robot'), ('robot', 'com', 'robot'),
            ('task', 'tin', 'state'), ('loc', 'lin', 'state'),
            ('robot', 'rin', 'state'), ('state', 'sin', 'state'),
            ('task', 'tto', 'value'), ('robot', 'rto', 'value'),
            ('state', 'sto', 'value'), ('value', 'vto', 'value'),
            ('task', 'take_time', 'robot'), ('robot', 'use_time', 'task')
        ]
        
        device = torch.device('cpu')
        policy_net = ScheduleNet4Layer(in_dim, hid_dim, out_dim, cetypes, 8).to(device)
        
        # Load checkpoint
        checkpoint = torch.load(model_path, map_location=device)
        policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
        
        alpha = checkpoint.get('alpha', 0.5)
        beta = checkpoint.get('beta', 0.5)
        
        print(f"✓ Model loaded successfully")
        print(f"✓ Training parameters: α={alpha:.3f}, β={beta:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error loading model: {e}")
        return False


def test_baseline_solvers():
    """Test baseline solvers with a simple instance."""
    print("\n=== Testing Baseline Solvers ===")
    
    # Find a test instance
    test_instance = None
    test_paths = [
        "./problem_instances_test/constraints/00001",
        "./problem_instances/constraints/00001"
    ]
    
    for path in test_paths:
        if os.path.isfile(f"{path}_dur.txt"):
            test_instance = path
            break
    
    if not test_instance:
        print("No test instance found for baseline testing")
        return False
    
    try:
        from makespan_for_each import solve_with_edf, solve_with_tercio, solve_with_random
        
        solvers = {
            'EDF': solve_with_edf,
            'Tercio': solve_with_tercio,
            'Random': solve_with_random
        }
        
        for solver_name, solver_func in solvers.items():
            try:
                mk, assigns, rt, ok = solver_func(test_instance, 5, 3)
                if ok:
                    if isinstance(assigns, list):
                        # Convert to dict
                        assign_dict = {r: [] for r in range(3)}
                        for task, robot in assigns:
                            assign_dict[robot].append(task)
                        assigns = assign_dict
                    
                    balance = calculate_workload_balance(assigns, 3)
                    print(f"✓ {solver_name}: makespan={mk:.2f}, balance={balance:.3f}")
                else:
                    print(f"✗ {solver_name}: Failed to solve")
                    
            except Exception as e:
                print(f"✗ {solver_name}: Error - {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing baseline solvers: {e}")
        return False


def main():
    """Run all tests."""
    print("Simple Multi-Objective Test Suite")
    print("=" * 40)
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Model Loading", test_model_loading),
        ("Baseline Solvers", test_baseline_solvers),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{test_name:20s}: {status}")
    
    total_passed = sum(1 for _, success in results if success)
    print(f"\nPassed: {total_passed}/{len(results)} tests")
    
    if total_passed == len(results):
        print("🎉 All tests passed! Multi-objective system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    main()
